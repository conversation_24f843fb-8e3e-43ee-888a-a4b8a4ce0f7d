# Liquid Glass 组件库（Figma 参数表 + 导入指南）

> 面向 iOS 26 Liquid Glass 视觉语言的**可交付组件清单**与**变量（Variables/Tokens）**。包含：命名规范、变量集合、组件参数、动效与可访问性、以及导入/发布为 Figma 团队库的步骤。

---

## 0. 命名与结构（建议）

- **文件名**：`iOS26 – Liquid Glass UI Kit`
- **页面结构**：
  1. `📁 Variables & Styles`（颜色/效果/半径/尺寸/动效变量 + 文本样式）
  2. `📁 Primitives`（图标/基本形状/遮罩）
  3. `📁 Components – Controls`（按钮/开关/滑块/分段控件/搜索框/...）
  4. `📁 Components – Surfaces`（Card/Sheet/Popover/Drawer/TopBar/TabBar/...）
  5. `📁 Patterns`（Paywall、主题卡、番茄抽屉、StandBy 布局）
  6. `📁 Spec`（标注与说明）
- **样式与变量命名**（示例）：`glass/tint/primary`, `elevation/2`, `radius/16`, `opacity/comfort`, `blur/24`, `highlight/strong`
- **变体属性命名**：`Kind=Primary|Secondary|Tertiary`，`Size=S|M|L`，`State=Default|Pressed|Disabled`，`Mode=Standard|Comfort|Solid`

---

## 1. 变量（Variables/Tokens）

> 建议以 Figma **Variables** 建 4 类集合：`Color`、`Effect`、`Radius`、`Dimens`（含间距/尺寸/不透明度/动效时间）。为 Light/Dark 建立 **Mode**；为视觉偏好再建 `Comfort`（高对比/低透明）与 `Solid`（不透明）额外 Mode。

### 1.1 Color（Light / Dark / Comfort / Solid）

| 变量名                    | 类别    | Light 值                  | Dark 值          | Comfort/Solid 说明                                        |
| ---------------------- | ----- | ------------------------ | --------------- | ------------------------------------------------------- |
| `glass/tint/primary`   | color | #FFFFFF @ 12%            | #000000 @ 24%   | Comfort：Light 8% / Dark 28%；Solid：改为纯色卡片（参见 `solid/bg`） |
| `glass/tint/secondary` | color | #FFFFFF @ 8%             | #000000 @ 16%   | 用于弱层级控件                                                 |
| `glass/highlight`      | color | #FFFFFF @ 35% → 0%（径向渐变） | 同               | 高光渐变顶端 35%                                              |
| `glass/stroke`         | color | #FFFFFF @ 14%            | #FFFFFF @ 8%    | 1px 玻璃描边                                                |
| `solid/bg`             | color | #0B0C0E                  | #0B0C0E         | Solid 模式下控件底色                                           |
| `text/primary`         | color | #FFFFFF @ 92%            | #FFFFFF @ 92%   | 文本/数字主色（置顶层）                                            |
| `text/secondary`       | color | #FFFFFF @ 64%            | #FFFFFF @ 64%   | 次要文本                                                    |
| `accent/aqua`          | color | #66D1FF                  | #66D1FF         | 强调描边/选中高光                                               |
| `accent/amber`         | color | #FFC56B                  | #FFC56B         | 备选强调                                                    |
| `bg/surface`           | color | #000000 @ 0–40%          | #000000 @ 0–60% | 画布/壁纸叠加（仅参考）                                            |

> 注：渐变在 Figma 以 `Local style: gradient/highlight` 定义；文本颜色需确保对比度 ≥ 7:1。

### 1.2 Effect（阴影/高光/模糊）

| 变量名           | 类别          | 值（外阴影）                                        | 备注      |
| ------------- | ----------- | --------------------------------------------- | ------- |
| `elevation/1` | drop-shadow | y:1, blur:6, spread:0, color:#000000 @ 8%     | 轻浮起     |
| `elevation/2` | drop-shadow | y:6, blur:16, spread:0, color:#000000 @ 10%   | 默认控件    |
| `elevation/3` | drop-shadow | y:12, blur:24, spread:0, color:#000000 @ 12%  | 大型表面    |
| `elevation/4` | drop-shadow | y:20, blur:40, spread:-2, color:#000000 @ 14% | 顶层抽屉/浮层 |

| 变量名                | 类别         | 值（内高光）                               | 备注   |
| ------------------ | ---------- | ------------------------------------ | ---- |
| `highlight/soft`   | inner-glow | center, blur:16, color:#FFFFFF @ 10% | 边缘提亮 |
| `highlight/strong` | inner-glow | center, blur:24, color:#FFFFFF @ 16% | 强高光  |

| 变量名       | 类别         | 值（模糊） | 备注     |
| --------- | ---------- | ----- | ------ |
| `blur/16` | layer-blur | 16    | 玻璃基础模糊 |
| `blur/24` | layer-blur | 24    | 强化玻璃   |
| `blur/32` | layer-blur | 32    | 顶层玻璃   |

### 1.3 Radius / Stroke / Opacity / Spacing

| 变量名                                                             | 类别     | 值               |
| --------------------------------------------------------------- | ------ | --------------- |
| `radius/12`                                                     | radius | 12              |
| `radius/16`                                                     | radius | 16              |
| `radius/20`                                                     | radius | 20              |
| `stroke/hairline`                                               | number | 1               |
| `opacity/disabled`                                              | number | 0.38            |
| `opacity/pressed`                                               | number | 0.86            |
| `space/4` `space/8` `space/12` `space/16` `space/20` `space/24` | number | 4/8/12/16/20/24 |

### 1.4 Motion（时长/缓动）

| 变量名                    | 类别           | 值                  |
| ---------------------- | ------------ | ------------------ |
| `motion/du/fast`       | number       | 180ms              |
| `motion/du/medium`     | number       | 220ms              |
| `motion/du/drawer`     | number       | 260ms              |
| `motion/ease/standard` | cubic-bezier | (0.2, 0.0, 0.2, 1) |
| `motion/ease/decel`    | cubic-bezier | (0.0, 0.0, 0.2, 1) |
| `motion/ease/accel`    | cubic-bezier | (0.4, 0.0, 1, 1)   |

---

## 2. 文本样式（Text Styles）

| 样式名             | 字体                              | 用途                | 备注               |
| --------------- | ------------------------------- | ----------------- | ---------------- |
| `type/digit/XL` | SF Pro Rounded Heavy 240 / -2%  | 大数字（iPad/StandBy） | 可变字体轴：Weight 900 |
| `type/digit/L`  | SF Pro Rounded Heavy 160 / -1%  | 大数字（iPhone 横置）    |                  |
| `type/digit/M`  | SF Pro Rounded Heavy 96 / -0.5% | 默认时钟数字            |                  |
| `type/label/M`  | SF Pro Text Medium 17           | 控件文案              |                  |
| `type/label/S`  | SF Pro Text Regular 13          | 次要说明              |                  |

— 注：字距为负值表示紧缩，需像素对齐；所有文本使用 `text/primary` / `text/secondary` 变量色。

---

## 3. 组件参数（Components）

> 所有组件基于 Auto Layout，引用上文变量；每个组件均含 `Mode` 变体：`Standard` / `Comfort` / `Solid`。

### 3.1 Button（GlassButton）

- **变体**：`Kind=Primary|Secondary|Tertiary`，`Size=S|M|L`，`State=Default|Pressed|Disabled`，`Mode=…`
- **尺寸与内边距**：
  - S：H44，Padding X: `space/12`，Y: `space/8`
  - M：H52，Padding X: `space/16`，Y: `space/12`
  - L：H60，Padding X: `space/20`，Y: `space/12`
- **样式**：
  - 填充：`glass/tint/primary`（Secondary 用 `…/secondary`）+ `blur/24`
  - 描边：`glass/stroke`（`stroke/hairline`）
  - 阴影：`elevation/2`（Pressed 态：`elevation/1`，并叠加 `highlight/soft`）
  - 文本：`text/primary`（Disabled：`opacity/disabled`）

### 3.2 IconButton（圆/方）

- **变体**：`Shape=Circle|Square`，`Size=S(36)/M(44)/L(52)`，`State`，`Mode`
- **样式**：同 Button；图标尺寸：S 16 / M 20 / L 24

### 3.3 SegmentedControl（Pill）

- **容器**：玻璃底（`glass/tint/secondary` + `blur/16`），描边 `glass/stroke`，半径 `radius/20`
- **选中项**：内部滑块使用 `glass/tint/primary` + `highlight/soft`，过渡 `motion/du/fast` + `ease/standard`

### 3.4 Switch（GlassSwitch）

- **轨道**：`glass/tint/secondary` + `blur/16`，描边 `glass/stroke`
- **拇指**：纯净层白色球体，外阴影 `elevation/2`，开启态描边 `accent/aqua`
- **尺寸**：H32，W52；拇指直径 28；半径 `radius/16`

### 3.5 Slider

- **轨道**：玻璃条（`glass/tint/secondary`），已选区叠加 `accent/aqua @ 32%`
- **拇指**：圆形纯净层，`elevation/2`，Pressed 态高光增强

### 3.6 SearchField / TextField

- **容器**：`glass/tint/primary` + `blur/24` + `elevation/1`；描边 `glass/stroke`
- **Icon**：左侧 20px；占位符 `text/secondary`

### 3.7 Card（GlassCard）

- **用途**：主题卡、设置分组卡
- **样式**：`glass/tint/primary` + `blur/24` + `elevation/2` + `highlight/soft`；半径 `radius/16`

### 3.8 Sheet / Drawer（BottomSheet）

- **样式**：`glass/tint/primary` + `blur/32` + `elevation/4`；顶部抓手纯净层
- **动效**：`motion/du/drawer` + `ease/decel`

### 3.9 Popover / Tooltip

- **样式**：次级玻璃（`…/secondary` + `blur/16`），`elevation/3`

### 3.10 TopBar / ToolBar（GlassBar）

- **高度**：96（含大标题态）；收缩后 64
- **样式**：`glass/tint/primary` + `blur/24`，底部分隔描边 `glass/stroke`

### 3.11 TabBar（GlassTabBar）

- **高度**：88（安全区外 48）
- **样式**：`…/primary` + `blur/24` + `elevation/3`；选中图标带 `accent/aqua` 高光描边

### 3.12 StandBy Widgets（时钟专用）

- **数字层**：**纯净层**（不折射/不模糊）；对比度 ≥ 7:1
- **边框**：极轻玻璃描边（可选），避免影响可读性

---

## 4. 可访问性（A11y）与模式

- `Mode=Comfort`：降低透明度（tint -30%）、提高描边和文本对比；移除 `highlight/strong`
- `Mode=Solid`：用 `solid/bg` 替换玻璃；保留阴影，去模糊
- 动效尊重“减少动态效果”设置：将过渡时长 -30%，禁用高光闪烁

---

## 5. 设计度量与网格

- 基线网格：4pt 系统；
- 边距：容器 16/20/24；
- 图标：16/20/24；
- Hairline：1px（在 @2x 为 0.5pt，Figma 仍以 1px 表示）。

---

## 6. 可选：Tokens JSON（供 Tokens Studio 等插件导入）

```json
{
  "$themes": [
    {"id":"light","name":"Light","selectedTokenSets":{"core": "source"}},
    {"id":"dark","name":"Dark","selectedTokenSets":{"core": "enabled"}},
    {"id":"comfort","name":"Comfort","selectedTokenSets":{"core": "enabled"}},
    {"id":"solid","name":"Solid","selectedTokenSets":{"core": "enabled"}}
  ],
  "core": {
    "glass": {
      "tint": {
        "primary": {"$type":"color","$value":"rgba(255,255,255,0.12)"},
        "secondary": {"$type":"color","$value":"rgba(255,255,255,0.08)"}
      },
      "stroke": {"$type":"color","$value":"rgba(255,255,255,0.14)"},
      "highlight": {"$type":"color","$value":"rgba(255,255,255,0.35)"}
    },
    "text": {
      "primary": {"$type":"color","$value":"rgba(255,255,255,0.92)"},
      "secondary": {"$type":"color","$value":"rgba(255,255,255,0.64)"}
    },
    "accent": {
      "aqua": {"$type":"color","$value":"#66D1FF"},
      "amber": {"$type":"color","$value":"#FFC56B"}
    },
    "elevation": {
      "1": {"$type":"boxShadow","$value":[{"color":"rgba(0,0,0,0.08)","x":0,"y":1,"blur":6,"spread":0}]},
      "2": {"$type":"boxShadow","$value":[{"color":"rgba(0,0,0,0.10)","x":0,"y":6,"blur":16,"spread":0}]},
      "3": {"$type":"boxShadow","$value":[{"color":"rgba(0,0,0,0.12)","x":0,"y":12,"blur":24,"spread":0}]},
      "4": {"$type":"boxShadow","$value":[{"color":"rgba(0,0,0,0.14)","x":0,"y":20,"blur":40,"spread":-2}]}
    },
    "blur": {"16":{"$type":"number","$value":16},"24":{"$type":"number","$value":24},"32":{"$type":"number","$value":32}},
    "radius": {"12":{"$type":"number","$value":12},"16":{"$type":"number","$value":16},"20":{"$type":"number","$value":20}},
    "opacity": {"disabled":{"$type":"number","$value":0.38},"pressed":{"$type":"number","$value":0.86}},
    "space": {"4":{"$type":"number","$value":4},"8":{"$type":"number","$value":8},"12":{"$type":"number","$value":12},"16":{"$type":"number","$value":16},"20":{"$type":"number","$value":20},"24":{"$type":"number","$value":24}}
  }
}
```

---

## 7. 导入与发布为 Figma 团队库（步骤）

**A）原生 Variables 方案（推荐）**

1. 新建文件 `iOS26 – Liquid Glass UI Kit` → 进入 **Variables 面板**：创建集合 `Color`、`Effect`、`Radius`、`Dimens`。

	2.	为集合添加 Modes：Light、Dark、Comfort、Solid，按上文表格录入数值（支持批量粘贴）。
	3.	在 📁 Variables & Styles 页，建立对应的 Local Styles：颜色（包括渐变）、效果（阴影/内发光/模糊）、文本样式（见 2）。
	4.	在 📁 Components – … 页，按“组件参数”章节搭建组件并绑定变量（颜色/效果/半径/动效）。设置变体属性：Kind/Size/State/Mode。
	5.	Assets → Library → Publish：将该文件发布为团队库。
	6.	在业务文件中 Assets → Libraries 启用该库，即可插入/同步组件与变量。

B）Tokens JSON + 插件（可选）
	1.	在该 UI Kit 文件中安装支持 Variables 的 Tokens 插件（如 Tokens Studio 等）。
	2.	将第 6 节提供的 Tokens JSON 导入插件并映射到 Figma Variables；
	3.	同步后，检查 Light/Dark/Comfort/Solid 四个 Mode 的变量是否正确映射，随后按步骤 A-4 绑定到组件。

C）复制法（快速试用）
	•	将本文件的 Components 页面整页复制到你的设计文件中；若变量缺失，Figma 会提示“Detach or Replace”，选择 Replace 并指向你文件中的同名变量。

⸻

## 8. 质量检查清单（在 Figma 内自检）

	•	Mode 切换：Standard/Comfort/Solid 三档在所有组件无断层/无色值遗漏
	•	Light/Dark：对比度 ≥ 7:1；数字层不被玻璃折射/模糊
	•	阴影/高光：Elevation 1–4 视觉层级清晰，无过曝
	•	Hairline：描边始终 1px；缩放 100% 检查像素对齐
	•	动效原型：按 motion/* 变量统一时长与缓动
	•	组件属性：Kind/Size/State/Mode 组合全部可用，Auto Layout 不破版

⸻

## 9. 附：典型组合预设（可直接拷贝）

	•	Primary GlassButton/M – Standard：tint=primary/12%、blur=24、elevation=2、stroke=glass/stroke、radius=16、padding=16x12、text=type/label/M
	•	GlassCard – Standard：tint=primary/12%、blur=24、elevation=2、highlight=soft、radius=16、padding=20
	•	BottomSheet – Standard：tint=primary/12%、blur=32、elevation=4、radius=20
	•	Segmented – Comfort：外层 tint=secondary/8%、选中滑块 tint=primary/8%、描边对比提高、取消强高光
	•	All – Solid：将 tint→solid/bg、移除 blur、保留 elevation 与描边，提高文本对比