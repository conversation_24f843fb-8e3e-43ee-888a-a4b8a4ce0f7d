# FlipClock2 测试计划（分层架构版）

## 1. 测试金字塔

- **单元测试（Unit）**：Domain 层服务（ClockEngine、FocusService、WhiteNoiseService、ThemeStore 等）、Application 层 ViewModel/Coordinator。覆盖率目标 ≥ 70%。
- **快照测试（Snapshot）**：关键 SwiftUI 视图（ContentView、PomodoroPage、Settings、WhiteNoisePanel），验证 UI 结构与主题。
- **UI 测试（UITest）**：核心用户流（启动 → 时钟 → 切换番茄 → 设置），确保无重大回归。
- **集成/端到端（E2E）**：TestFlight 手测长时间运行、通知、Live Activity。

## 2. 测试目录结构

```
FlipClock2Tests/
├── Domain/
│   ├── FocusServiceTests.swift
│   ├── WhiteNoiseServiceTests.swift
│   └── …
├── Application/
│   ├── ClockViewModelTests.swift
│   └── …
└── Snapshot/
    ├── PomodoroNavigationSnapshot.swift
    └── …

FlipClock2UITests/
└── Presentation/
    ├── ContentFlowTests.swift
    └── LaunchPerformanceTests.swift
```

## 3. 推荐测试工具

| 类型 | 工具 | 说明 |
|------|------|------|
| 单元测试 | XCTest + async/await | 覆盖服务与 ViewModel 逻辑 |
| 快照测试 | iOSSnapshotTestCase / SwiftSnapshotTesting（待引入） | 对关键视图建立 baseline |
| UI 测试 | XCTest UI | 自动化核心用户流 |
| 性能 | Instruments、Xcode Measure | 掉帧、启动时长、内存 |

## 4. CI 流程建议

```
fastlane test_all:
  - swiftformat + swiftlint
  - xcodebuild test -scheme FlipClock2 -destination 'platform=iOS Simulator,name=iPhone 16 Pro'
  - xcodebuild test -scheme FlipClock2Tests -destination …
  - xcodebuild test -scheme FlipClock2UITests -destination … (可选)
```

构建失败时输出日志并阻止合并。快照变更需评审后更新 baseline。

## 5. 回归清单

- 时钟翻页动画准确，秒针实时更新。
- 番茄钟启动/暂停/重置/Live Activity 同步。
- 白噪音播放控制与音量调节。
- 设置面板主题切换、视觉模式切换。
- 低电量/后台状态恢复。

## 6. 后续计划

- 引入 Snapshot 测试框架并完善测试覆盖。
- 为 `FocusCoordinator`、`WhiteNoiseService` 添加更多情景测试。
- 集成 Telemetry 模拟测试，验证事件上报。
