# FlipClock2 技术架构设计文档

- **文档版本**：v1.0
- **更新时间**：2025-09-24 23:45:10
- **适用范围**：FlipClock2 iOS 客户端（iOS 16+），含 Widget、StandBy、Live Activities
- **作者**：Codex（自动生成，供团队审阅与迭代）

## 1. 架构愿景与原则

1. **体验优先**：时钟翻页、Liquid Glass 视觉与专注工具保持 60fps、无感切换、低功耗。
2. **模块解耦**：以功能域拆包，Swift Package Manager 交付；UI、业务、系统集成分层。
3. **可演进性**：v1.0 聚焦 MVP；为 v1.1+（统计、同步、商城化）预留扩展点。
4. **隐私合规**：仅采集匿名指标，遵守 App Store 隐私标签要求。
5. **可测试性**：核心状态机、计时引擎、商业化流程可独立单测；关键 UI 具快照/交互测试。

## 2. 系统上下文

```
+------------------+        +-------------------+
|   Apple APIs     |        |   3rd-Party SDKs  |
| (ActivityKit,    |        | (Telemetry, Ads)  |
|  WidgetKit, etc) |        +---------+---------+
+---------+--------+                  |
          |                           v
          v                 +-------------------+
+---------------------+     |   Commerce/Ads    |
|      FlipClock2     |<--->| Providers (opt.)  |
|  (iOS App Bundle)   |     +-------------------+
+----+---------+------+               |
     |         |                      v
     |  +------+----------+   +-------------------+
     |  | Widget Extension|   | Live Activities   |
     |  +-----------------+   +-------------------+
     v
+-------------------+
|   Local Storage   |
|  (UserDefaults,   |
|   Keychain, etc.) |
+-------------------+
```

## 3. 模块化蓝图

| 模块 | 职责 | 关键点 |
|------|------|--------|
| `AppShell` | App 生命周期、依赖注入、路由、Feature Flag | `SceneDelegate`/`App` 主入口；组合 DI 容器；负责全局环境状态与设置面板呈现。 |
| `UI` | SwiftUI View 层（Clock、Pomodoro、Settings、WhiteNoise、Widgets） | 遵循单向数据流；使用 `@StateObject`/`@ObservedObject` 与 `ViewModel` 通信；尊重无障碍切换。 |
| `ClockEngine` | 时间采集、翻页调度、微位移算法、帧率策略 | 以 `Actor`/`AsyncStream` 暴露数据；可将 CADisplayLink 封装在 UIKit 桥接层；提供模拟器模式。 |
| `ThemeKit` | 主题、视觉模式、玻璃材质参数 | 主题 JSON / 本地配置；支持动态下载与缓存；公开 `Theme`、`GlassSpec` 和配色转换工具。 |
| `FocusKit` | 番茄钟状态机、通知调度、Live Activity 协调 | `FocusService` 为主入口；与 `NotificationCenter`、`ActivityKit` 通信；即插即用以便未来扩展统计。 |
| `AudioKit` | 白噪音播放、音频会话管理 | `AVAudioEngine` 封装、音量同步、后台播放策略；提供模拟模式。 |
| `EcoKit` | WidgetKit、StandBy、ActivityKit 交互 | 按目标拆分 Targets；共享 ViewModel/数据访问；统一数据编码策略。 |
| `CommerceKit` | StoreKit2、权益缓存、付费墙策略 | 负责产品加载、交易流程、试用与恢复；引出 `EntitlementState` 供 UI 订阅。 |
| `AdsKit` | 广告提供、频控、A/B 切换 | 仅在设置/主题页注入视图；独立协议方便空实现。 |
| `StorageKit` | 偏好与数据持久化（UserDefaults、Keychain、CoreData/SwiftData） | 采用仓储模式；提供数据迁移框架；支持未来 iCloud 同步。 |
| `TelemetryKit` | 匿名事件、性能、崩溃监控 | 抽象上报接口；默认 TelemetryDeck/Sentry；尊重用户关闭诊断设置。 |

> 现阶段源代码尚未拆分 SPM 包，上述模块化方案可逐步实施：优先抽离 `ClockEngine`、`FocusKit`、`ThemeKit`，逐步替换视图内耦合逻辑。

## 4. 分层结构

```
Presentation Layer (SwiftUI Views, ViewModels)
  ↑ 依赖注入
Domain Layer (ClockEngine, FocusKit, ThemeKit, AudioKit)
  ↑ 协议抽象
Infrastructure Layer (StorageKit, CommerceKit, AdsKit, TelemetryKit)
  ↑
System/APIs (ActivityKit, WidgetKit, AVFoundation, StoreKit2)
```

- **Presentation Layer**：保持无状态视图；`ViewModel` 仅负责组合领域服务输出；多场景（主 App、Widget、Live Activity）共享 `ViewModel`/`Reducer` 以减少重复逻辑。
- **Domain Layer**：包含纯 Swift 逻辑；对平台依赖通过协议/注入隔离；便于单元与集成测试。
- **Infrastructure Layer**：与系统 API/外部 SDK 交互；通过协议向上游暴露能力。

## 5. 关键数据流

### 时钟刷新
1. `ClockViewModel.start()` 启动 `ClockEngine.start()`。
2. `DisplayLinkClockEngine` 使用 `CADisplayLink` → 采样系统时间 → 生成 `ClockSnapshot`。
3. `AsyncStream` 向 `ClockViewModel` 推送；`@Published snapshot` 驱动 `FlipClockView`。
4. `FlipDigitView` 内部队列处理数字翻转动画，保证顺序与合并更新。

### 番茄钟 + Live Activity
1. `PomodoroPageView` 触发 `FocusService.start()`/`pause()`/`resume()`。
2. `FocusService` `Task` 驱动 `ContinuousClock` 睡眠，每 200ms 更新剩余时间。
3. `FocusLiveActivityCoordinator` 订阅 `service.$snapshot`，在 ActivityKit 可用时同步状态。
4. `NotificationScheduler`（待实现）在阶段切换时发本地通知。

### 白噪音
1. `WhiteNoisePanelView` 选择音色 → `WhiteNoiseService.play(profile:)`。
2. 服务确保 `AVAudioSession` 配置一次、预生成 `AVAudioPCMBuffer`，循环播放。
3. UI 通过 `@Published` 状态更新播放按钮、音量滑块。

## 6. 并发与线程策略

| 场景 | 策略 | 备注 |
|------|------|------|
| 时钟刷新 | `CADisplayLink` + 主线程 `@MainActor` | 避免跨线程更新 UI；异步流保持背压。 |
| 番茄钟 | `Task` + `ContinuousClock` | 放在 `@MainActor`，但耗时计算在后台队列；合适时转为 `actor` 管理状态。 |
| 白噪音 | `AVAudioEngine` 线程安全，公共方法标记 `@MainActor` 以简化调用。 |
| Commerce/网络 | `async/await` 与 `Task { }`，在视图层捕获错误并降级。 |

- 使用 `actor` 封装有状态服务（如 `FlipScheduler`, `BurnInProtector`）防止竞态。
- 长寿命任务在 `deinit` 或 `scenePhase` 切换时取消，避免内存泄漏。

## 7. 状态管理与依赖注入

- 推荐引入 `DependencyContainer`（或 Swift Concurrency `@MainActor` 单例）集中初始化服务。
- `ContentView` 通过 `@Environment` 提供共享 `ThemeStore`、`FocusService`、`WhiteNoiseService` 等实例。
- Widget/Live Activity 使用 `SharedState`（App Group UserDefaults / File Storage）共享轻量数据。规划在 v1.1 实现。

## 8. 配置与 Feature Flag

| 配置项 | 存储 | 说明 |
|--------|------|------|
| 主题/视觉模式 | `ThemeStore` + `UserDefaults` | 以 `Codable` 保存主题 ID、模式；支持远程下发扩展。 |
| 番茄预设 | `UserDefaults` | 自定义预设数组；修改同步至当前运行实例。 |
| 白噪音音量 | `UserDefaults` | 与系统设置保持一致；退出时持久化。 |
| 商业化参数 | `RemoteConfig`（可选） | 通过 JSON/Feature Flag 控制价格、付费墙展示策略。 |

Feature Flag 建议采用 `AppShell` 注入：`FeatureFlags(isWhiteNoiseEnabled: Bool, showAds: Bool, enableTelemetry: Bool)`，并在单元测试中覆盖。

## 9. 错误处理与可观测性

- **错误分级**：
  - 交互错误（购买失败、音频权限）：弹出 `AlertToast` 或设置内提示。
  - 系统错误（ActivityKit 授权、音频引擎）：记录 `TelemetryKit.log(error:)`，降级 UI。
  - 致命错误：如 `AVAudioPCMBuffer` 分配失败，`assertionFailure` + 记录。
- **日志**：统一 `Logger.category`（`clock`, `focus`, `commerce`, `audio`, `telemetry`）；发布版降级到 `info`/`error`。
- **监控指标**：帧率、CPU、内存、崩溃率、购买漏斗、广告曝光；通过 Telemetry/Analytics 上报（匿名）。

## 10. 测试策略

| 层级 | 工具 | 内容 |
|------|------|------|
| 单元测试 | XCTest + `async/await` | `ClockEngine` 时间精度；`FocusService` 状态机；`WhiteNoiseService` 状态切换；`ThemeStore` 模式切换。 |
| UI 测试 | XCTest UI / Snapshot | `ContentView` 场景（时钟/番茄）快照；设置面板主题切换；支付壁垒（Mock）。 |
| 集成测试 | TestFlight 手动/脚本 | Widget/Live Activity 与主 App 同步；音频后台播放；通知触发。 |
| 性能测试 | Instruments | 掉帧、内存、能源；翻页动画、音频播放、长时间运行。 |
| 自动化检查 | SwiftLint/SwiftFormat | 保持代码风格一致。 |

建议在 `CI` 上配置：`xcodebuild test -scheme FlipClock2 -destination 'platform=iOS Simulator,name=iPhone 16 Pro'`（可附加并发测试）。

## 11. 构建与发布

1. **依赖管理**：全部 SPM；第三方 SDK（Telemetry、广告）封装为私有 Package，便于版本锁定。
2. **CI/CD**：
   - Lint → Build → Unit Tests → UI Tests（可选） → 生成 `.ipa`。
   - 使用 `fastlane` 自动签名、上传 TestFlight、生成截图。
3. **配置分离**：Debug/Release/AdHoc Profile；在 CI 中通过环境变量注入 Telemetry/Ads Key。
4. **版本策略**：语义化（Major.Minor.Patch）；与路线图对齐。

## 12. 安全与隐私

- 仅存储必要偏好，使用 Keychain 保存订阅权益哈希/收据。
- 白噪音后台播放需显式提示；提供快速关闭入口。
- Telemetry 提供光学开关；默认开启匿名事件收集，关闭后立即停止上传。
- 不采集位置、联系人等敏感数据；隐私政策随 App 发布更新。

## 13. 性能目标

| 指标 | 目标值 | 备注 |
|------|--------|------|
| 冷启动 | < 1.5s | 首屏时钟可见；懒加载次要服务。 |
| 时钟帧率 | 60 fps（低端 ≥ 40 fps） | 低电量模式允许降帧；翻页动画无撕裂。 |
| 内存占用 | < 150 MB | 重点关注长时间放置、音频播放状态。 |
| 电池消耗 | 60 分钟播放番茄/白噪音 < 6%（iPhone 16 Pro） | Instruments Energy Log 验证。 |

## 14. 演进路线

- **v1.0 → v1.1**：抽离 `ThemeKit`/`FocusKit` 包，添加统计存储（CoreData/SwiftData）；实现 Shared App Group 同步以支持 Widget 实时刷新。
- **v1.1 → v1.2**：引入 `SyncKit`（iCloud 同步）、`ThemeEditor`；扩展 Telemetry 事件（保留匿名）。
- **v1.2 → v1.3**：Catalyst/Mac 版复用领域层；Apple Watch Companion 通过 `WatchConnectivity` 与 `FocusKit` 同步。

## 15. 开放问题

1. **商业化 SDK 选型**：是否采用 RevenueCat（便捷）或原生 StoreKit2（可控）；需要在 v1.0 决策。
2. **广告位实现**：若选择第三方 SDK，需要安全地隔离至 `AdsKit`，避免对主体验产生性能影响。
3. **Telemetry 平台**：TelemetryDeck/Self-hosted？需明确数据驻留与成本。 
4. **音频授权与后台策略**：是否在默认启动时申请后台音频能力？影响审核与功耗。
5. **Widget 实时性**：若要展示秒级更新，需要使用 `ActivityKit` 或 `AppIntents`；需评估功耗与平台限制。

---

> 本文档可作为后续设计/评审的基础，请在实现过程中保持更新，并将新模块/依赖纳入上述分层与模块化框架。
