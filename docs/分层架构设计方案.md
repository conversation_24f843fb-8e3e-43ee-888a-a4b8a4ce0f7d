# FlipClock2 分层架构设计方案

- **文档版本**：v1.0
- **审批状态**：待确认
- **适用范围**：FlipClock2 iOS 客户端（含 Widget、StandBy、Live Activities、未来商业化扩展）
- **编写日期**：2025-09-24
- **作者**：Codex（协助草拟，待团队评审）

---

## 1. 架构目标与原则

1. **清晰分层**：确保界面、业务、系统服务解耦，降低耦合、便于测试。  
2. **模块化演进**：围绕时钟、专注、白噪音、主题等核心域拆分模块，满足后续商业化、统计、同步等需求。  
3. **性能优先**：翻页时钟和 Liquid Glass 视觉需维持 60fps，分层不可引入不必要开销。  
4. **可测试性**：领域服务需可单测，UI 可通过快照/集成测试验证。  
5. **最小化依赖**：尽量使用系统框架；如需第三方（Telemetry/RevenueCat/广告），通过基础设施层隔离。  
6. **易部署易维护**：目录结构与 Swift Package 规划保持一致，便于 CI/CD 与协作。

---

## 2. 应用整体架构

```
┌────────────────────────────────────────────────────────┐
│                     Presentation Layer                 │
│  SwiftUI Views · View Components · Live Activity UI    │
└───────────────▲────────────────────────────────────────┘
                │ Binds via ViewModels / Coordinators
┌───────────────┴────────────────────────────────────────┐
│              Application Layer (State & Flow)          │
│  ViewModels · SceneCoordinators · Interaction Logic    │
└───────────────▲────────────────────────────────────────┘
                │  Protocol-oriented services
┌───────────────┴────────────────────────────────────────┐
│                      Domain Layer                       │
│  ClockEngine · FocusKit · AudioKit · ThemeKit           │
│  LayoutMetrics · Burn-in protection · Business Rules    │
└───────────────▲────────────────────────────────────────┘
                │  Infrastructure adapters (async/await)  
┌───────────────┴────────────────────────────────────────┐
│                   Infrastructure Layer                  │
│  Persistence · Notification · Commerce · Ads · Telemetry│
│  System Integrations (ActivityKit, WidgetKit, AVFoundation)│
└─────────────────────────────────────────────────────────┘
```

- **依赖方向**：上层只依赖下层暴露的协议/模型；禁止反向引用。  
- **交互方式**：View → ViewModel（`@StateObject/@ObservedObject`）→ Service 协议 → 具体实现（Domain/Infrastructure）。

### 2.1 模块与职责

| 层级 | 模块 | 主要职责 | 备注 |
|------|------|----------|------|
| Presentation | `ClockDashboard`, `PomodoroPage`, `Settings`, `WhiteNoisePanel`, `GlassComponents` | SwiftUI 视图、动画、输入处理、路由入口 | 仅通过 ViewModel 获取状态；可拆分为 `Views/` 与 `Components/` |
| Application | `ClockCoordinator`, `FocusCoordinator`, `AppScene`, `LiveActivityPresenter` | 生命周期、状态聚合、导航 & Sheet 管理、Live Activity 触发 | 负责依赖注入，防止视图直接触碰服务 |
| Domain | `ClockEngine`, `FocusService`, `AudioService`, `ThemeStore`, `LayoutMetrics`, `BurnInProtection` | 业务算法、状态机、时间采样、翻页调度、主题管理 | 对外暴露协议（如 `ClockEngineType`）方便 Mock |
| Infrastructure | `PersistenceStore`, `NotificationCenterAdapter`, `CommerceGateway`, `AdsProvider`, `TelemetryClient`, `WidgetBridge` | 系统 API 封装、外部 SDK 适配、数据持久化（UserDefaults/Keychain/CoreData） | 可按需替换实现（如不同广告平台） |
| Shared | `CommonUI`, `Extensions`, `Utilities`, `Logging`, `Configuration` | 跨层工具、常量、协议定义 | 保持轻量，避免成为“垃圾箱” |

---

## 3. 技术栈选择与依赖管理

### 3.1 核心技术栈

| 领域 | 使用框架/技术 | 说明 |
|------|---------------|------|
| UI | SwiftUI, CoreAnimation, GeometryReader, Custom Animations | 构建翻页卡片、玻璃质感、动画交互 |
| 并发 | Swift Concurrency (`async/await`, `Task`, `AsyncStream`, `actor`), Combine（兼容层） | 主线程安全、时间驱动、状态通知 |
| 时钟引擎 | QuartzCore (`CADisplayLink`), Foundation (`Calendar`) | 60fps 时间采样、翻页节奏控制 |
| 专注引擎 | Swift Concurrency `ContinuousClock`, ActivityKit | 番茄计时、Live Activity 推送 |
| 音频 | AVFoundation (`AVAudioEngine`, `AVAudioPlayerNode`) | 白噪音循环、后台播放 |
| 主题 | SwiftUI Color/Material、自定义 `GlassSpec`, Figma Tokens | Liquid Glass 主题体系 |
| 系统集成 | ActivityKit, WidgetKit, UserNotifications, App Intents (未来) | 提供 Live Activity/Widget/通知集成 |
| 商业化（规划） | StoreKit 2, RevenueCat (备选) | 订阅、试用、权益管理 |
| Telemetry（规划） | TelemetryDeck / 自建端点 | 匿名事件、性能指标 |

### 3.2 依赖管理策略

- **优先系统框架**，目前无第三方依赖，未来引入商业化/Telemetry SDK 时通过 `Infrastructure` 注入。  
- **Swift Package Manager**：逐步将 `Domain`、`Infrastructure` 子系统抽成 SPM 包，便于多 Target 共享（主 App/Widget/Live Activity）。  
- **模块边界**：每个模块通过 Protocol + Implementation 暴露；禁止跨层直接 import 具体实现。  
- **版本策略**：目标 Swift 5.9 / Xcode 16+；最低支持 iOS 16（配合 ActivityKit、StandBy），如需兼容 iOS 15，基础设施层需提供降级实现。  
- **配置管理**：使用 `xcconfig`/`Environment.plist` 注入 API Key、开关，避免硬编码。

---

## 4. 目录结构规划

```
FlipClock2/
├── AppShell/
│   ├── FlipClock2App.swift
│   ├── AppSceneCoordinator.swift
│   └── DependencyContainer.swift
├── Presentation/
│   ├── Views/
│   │   ├── ContentView.swift
│   │   ├── ClockDashboard/
│   │   │   ├── FlipClockView.swift
│   │   │   └── FlipDigitView.swift
│   │   ├── Pomodoro/
│   │   │   └── PomodoroPageView.swift
│   │   ├── Settings/
│   │   │   └── SettingsView.swift
│   │   ├── WhiteNoise/
│   │   │   └── WhiteNoisePanelView.swift
│   │   └── Shared/
│   │       ├── GlassBackground.swift
│   │       └── Components.swift
│   └── LiveActivities/
│       └── FocusLiveActivityView.swift
├── Application/
│   ├── ViewModels/
│   │   ├── ClockViewModel.swift
│   │   ├── ThemeViewModel.swift
│   │   └── FocusPanelViewModel.swift
│   └── Coordinators/
│       ├── FocusCoordinator.swift
│       └── SettingsCoordinator.swift
├── Domain/
│   ├── Clock/
│   │   ├── ClockEngine.swift
│   │   └── LayoutMetrics.swift
│   ├── Focus/
│   │   ├── FocusKit.swift
│   │   └── FocusModels.swift
│   ├── Audio/
│   │   └── WhiteNoiseService.swift
│   ├── Theme/
│   │   ├── Theme.swift
│   │   └── ThemeStore.swift
│   └── Shared/
│       └── Utilities.swift
├── Infrastructure/
│   ├── Persistence/
│   │   └── UserDefaultsStore.swift
│   ├── Notifications/
│   │   └── NotificationScheduler.swift
│   ├── Commerce/
│   │   └── StoreKitService.swift (planned)
│   ├── Ads/
│   │   └── AdsProvider.swift (planned)
│   └── Telemetry/
│       └── TelemetryClient.swift (planned)
├── Shared/
│   ├── Extensions/
│   │   └── View+Compat.swift
│   ├── Configuration/
│   │   └── FeatureFlags.swift
│   └── Logging/
│       └── Logger.swift
├── Resources/
│   └── Assets.xcassets
├── WidgetExtension/
│   └── …（使用同一 Domain & Infrastructure）
└── LiveActivitiesExtension/
    └── …
```

> **迁移步骤建议**：先创建文件夹/Group，再按模块移动现有文件；更新 import 与 Preview。后续再按实际需要新增 Coordinator、基础设施占位文件。

### 4.1 测试目录

```
FlipClock2Tests/
├── Domain/
│   ├── ClockEngineTests.swift
│   ├── FocusServiceTests.swift
│   └── WhiteNoiseServiceTests.swift
├── Application/
│   └── ClockViewModelTests.swift
└── Snapshot/
    └── ClockDashboardSnapshotTests.swift

FlipClock2UITests/
└── Presentation/
    ├── ContentFlowTests.swift
    └── LaunchPerformanceTests.swift
```

---

## 5. 关键交互与数据流

### 5.1 时钟刷新

1. `ClockDashboardView` 绑定 `ClockViewModel`。  
2. `ClockViewModel` 持有 `ClockEngineType`（协议），启动时调用 `engine.start()`。  
3. `DisplayLinkClockEngine`（Domain 实现）使用 `CADisplayLink` 每帧生成 `ClockSnapshot`。  
4. `AsyncStream` 推送至 `ClockViewModel` → 发布给 UI。  
5. `FlipDigitView` 根据变化执行翻页动画。

### 5.2 番茄钟 & Live Activity

1. `PomodoroPageView` 调用 `FocusCoordinator` 控制 `FocusServiceType`。  
2. `FocusService` 更新 `FocusTickerSnapshot`，通过 `@Published`/`AsyncStream` 通知。  
3. `FocusLiveActivityPresenter`（Application 层）监听快照，协调 `ActivityKit` 更新/结束。  
4. `NotificationScheduler` 在阶段切换时触发本地通知（Infrastructure 层）。

### 5.3 白噪音播放

1. UI 调用 `AudioServiceType.play(profile:)`。  
2. `WhiteNoiseService` 管理 `AVAudioEngine`，预生成缓冲并循环播放。  
3. 状态回传给 ViewModel → 更新 UI。

### 5.4 主题切换

1. 设置页更新 `ThemeStoreType`，持久化于 `PersistenceStore`。  
2. ViewModel 广播主题变化，UI 自动重绘；Live Activity / Widget 通过共享存储读取最新主题。

---

## 6. 依赖注入与配置

- `DependencyContainer` 在 `App` 启动时构建默认实现：
  - `ClockEngineType` → `DisplayLinkClockEngine`
  - `FocusServiceType` → `FocusService`
  - `AudioServiceType` → `WhiteNoiseService`
  - `ThemeStoreType` → `ThemeStore`
  - `PersistenceStoreType` → `UserDefaultsStore`
  - `NotificationSchedulerType` → `UNUserNotificationCenterAdapter`
- 通过 `EnvironmentKey` 或 `ObservableObject` 将容器注入 `ContentView`。  
- 测试环境可创建 `MockDependencyContainer` 替换具体实现。

---

## 7. 安全、性能与监控

- **性能**：保持时钟渲染在主线程；后台服务（音频、番茄）使用 `Task` 与 `actor` 避免竞态。  
- **能源**：`BurnInProtection` 控制微位移；低电量/高温时通过 `PerformancePolicy` 降帧或减少特效。  
- **隐私**：所有持久化数据仅限必要偏好；商业化/广告在基础设施层实现隐私合规逻辑。  
- **监控**：`TelemetryClient` 记录关键事件（AppOpen、ApplyTheme、StartPomodoro 等）；支持用户关闭诊断收集。  
- **错误处理**：服务层通过 `Logger` 报错 + UI 提示；严重错误上报监控平台。

---

## 8. 实施路线与成本

| 阶段 | 主要任务 | 预估 | 产出 |
|------|----------|------|------|
| Phase 1 | 建立目录、DependencyContainer、协议接口，迁移现有文件 | 2–3 天 | 新分层结构编译通过，功能无回归 |
| Phase 2 | 拆分测试目录、补充 ViewModel/Service 单测 | 1–2 天 | 单测覆盖关键逻辑，CI 可用 |
| Phase 3 | 视图微调、引入 Coordinator、准备 Widget/Live Activity 共享 | 2 天 | 便于后续功能扩展 |
| Phase 4 | 规划 SPM 包拆分（Domain/Infrastructure） | 视后续需求 | 多 Target/平台复用 |

---

## 9. 开放问题

1. **商业化方案落地**：确定 StoreKit 原生 vs RevenueCat，并补充 `CommerceGateway` 实现。  
2. **Telemetry 平台选择**：TelemetryDeck、自建端点或 Mixpanel？需结合隐私策略决定。  
3. **Widget 数据实时性**：Widget 是否需要秒级更新？可能需要 App Group & Live Activity 支撑。  
4. **多平台扩展**：若推出 Catalyst/Mac/Watch 版本，Domain/Infrastructure 层需保持平台无关性。  
5. **团队协作流程**：是否需要在 CI 中强制分层引入规则（SwiftLint、自定义脚本）以避免结构回退。

---

> 本文档为架构蓝图，建议在团队评审后锁定目录和模块命名，并纳入项目 README 或内部 Wiki。后续每次迭代需同步更新。
