# 翻页时钟布局指标说明

本文档描述应用如何在不同方向、不同设备以及功能组合（开启/关闭秒钟、番茄钟控制条等）下，保持翻页时钟组件的可读性。我们提供竖屏与横屏两套计算规则，SwiftUI 视图会按照这些指标渲染卡片网格。

## 公共输入

两个计算器都依赖以下上下文信息：

- `contentSize`：扣除安全区以及外部控件（顶部按钮、底部面板、番茄钟控制条等）后的可用区域宽高。
- `columns`：数字列数。显示秒钟时为 6 列，否则为 4 列。
- `rows`：数字行数。竖屏下有 2 行或 3 行，横屏目前是 1 行，但依旧参数化。
- `edgePadding`：防止卡片贴住屏幕边缘的最小外边距。
- `aspectRatio`：翻页卡片的宽高比（默认 1 : 1.3）。
- `extraVerticalPadding`（仅横屏）：预留给番茄钟控制条等其他元素的额外垂直空间。

计算结果统一封装为 `ClockLayoutMetrics`，包含：

- `cardSize`：单个卡片最终宽高。
- `font`：数字字体大小。
- `digitSpacing`：同一行卡片之间的水平间距。
- `rowSpacing`：行与行之间的垂直间距（竖屏适用）。
- `outerPadding`：包裹整行卡片时使用的外边距。
- `innerPadding`：行/卡片内部的补充内边距。
- `scale`：旧版横屏视图遗留的整体缩放因子，默认为 `1`。

## 竖屏算法

1. **初始宽度估算**：假设每行固定两列数字，先在可用宽度中扣除双倍外边距与最小列间距，计算原始卡片宽度：
   ```
   rawWidth = (contentWidth - 2*edgePadding - (columnsPerRow - 1)*minColumnSpacing)
              / columnsPerRow
   ```
2. **卡片高度**：乘以宽高比得到 `rawHeight`。
3. **派生间距**：行间距取 `max(rawWidth * 0.18, minRowSpacing)`。水平方向的间距与内边距同样按照卡片宽度等比例缩放，并设定最小值防止在小屏设备上过窄。
4. **高度校验**：计算布满 `rows` 行后所需的高度：
   ```
   requiredHeight = rows * rawHeight + (rows - 1) * rowSpacing
                    + 2 * innerVerticalPadding
   ```
   若超过可用高度，统一缩放所有水平指标（宽度、间距、字体等），直至满足限制。
5. **宽度校验**：用缩放后的宽度重新验证外边距约束（例如极窄的 iPhone SE）。若仍然越界，再次等比例缩放，以保证整体落在 `contentSize` 内。
6. **字体与内边距**：字体大小取 `cardWidth * 0.78`，其余 padding 也按照宽/高的固定占比计算。

当关闭秒钟（`rows = 2`）时，这套公式自然得出更大的卡片；开启秒钟（`rows = 3`）时则会自动缩小、保持间距一致。

## 横屏算法

1. **初始宽度**：直接用可用宽度套用下式：
   ```
   rawWidth = (contentWidth - 2*edgePadding - (columns - 1)*minColumnSpacing)
              / columns
   ```
2. **控制栏分区**：番茄钟横屏需要在卡片右侧竖排三个按钮，因此要预留一段固定宽度的控制栏（`buttonSize + 2*edgePadding`），并与卡片区域保持垂直居中。
3. **最大宽度限制**：仍需给卡片宽度设置上限（如 `contentWidth * 0.18`），同时保留最小值以兼容窄屏或分屏场景。
4. **高度预算**：在扣除 `extraVerticalPadding` 后得到 `maxCardHeight`，若 `rawWidth * aspectRatio` 超出则按比例缩小。对带控制栏的场景，还要保证卡片高度至少能容纳竖排按钮的总高度，防止按钮溢出。
5. **最终间距**：横向间距随卡片尺寸调整，但不低于 `16pt`；外边距依据剩余宽度重新计算，使卡片区域居中且控制栏紧贴一侧。由于当前横屏只显示一行数字，`rowSpacing` 维持 `0`。
6. **字体与内边距**：继承竖屏的计算方式，保证翻页卡片与按钮在不同方向下风格统一；侧栏按钮的对齐同样依赖该高度指标。

通过提前划分控制栏并收紧高度/宽度约束，横屏番茄钟既能留出按钮位置，又不会将标题挤出安全区域。

## 实现要点

- 将两个计算器放在可复用的工具中，`FlipClockView` 与 `PomodoroPageView` 根据自身需求传入参数。竖屏模式使用 `rows = showsSeconds ? 3 : 2`，横屏模式使用 `rows = 1`、`columns = showsSeconds ? 6 : 4`（番茄钟则永远是 `4`）。
- 在调用计算器前，先从 GeometryReader 中剔除安全区与上下控件占用的空间。
- 所有派生值应由输入确定，避免动画中出现跳动。

## 验证清单

调整常量后，请遍历下表确认没有溢出或重叠现象：

| 设备               | 方向   | 秒钟 | 模式       | 预期表现                         |
|--------------------|--------|------|------------|----------------------------------|
| iPhone 16 Pro      | 竖屏   | 开   | 时钟       | 三行居中，无重叠                 |
| iPhone 16 Pro      | 竖屏   | 关   | 时钟       | 两行更大，间距均匀               |
| iPhone 16 Pro      | 横屏   | 开   | 时钟       | 单行显示，尺寸不过宽             |
| iPhone 16 Pro      | 横屏   | 关   | 番茄钟     | 卡片受限制，控制条完全可见       |
| iPad Pro 13"       | 横/竖 | 开/关 | 时钟/番茄钟 | 卡片随尺寸变化但不溢出           |
| iPhone SE (小屏)   | 竖屏   | 开   | 时钟       | 自动缩放，保持可读性             |

如有任何偏差，请记录并在提交前调整。 
