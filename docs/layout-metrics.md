# Flip Clock Layout Metrics

This document outlines the sizing rules the app uses to keep the flip-clock
widgets readable across orientations, devices and feature sets (seconds on/off,
Pomodoro controls, etc.). Two metric calculators are used: one for portrait and
one for landscape compositions. Both produce the sizes and spacing that the
SwiftUI views consume when building the card grid.

## Shared Inputs

Each calculator receives the following contextual values:

- `contentSize`: The available width and height after removing the safe-area
  insets and any external chrome (top buttons, bottom sheets, Pomodoro controls
  etc.).
- `columns`: Number of digit columns. Six columns when seconds are visible,
  otherwise four.
- `rows`: Number of digit rows. Portrait shows two or three rows, landscape is
  always a single row today but remains parameterised.
- `edgePadding`: Minimum outer padding to keep the deck from touching the
  window edges.
- `aspectRatio`: The flip-card aspect ratio (width : height). We use 1 : 1.3.
- `extraVerticalPadding` (landscape only): Additional vertical space to reserve
  for elements such as the Pomodoro control bar.

Both calculators output a `ClockLayoutMetrics` structure containing:

- `cardSize`: The final card width/height per digit.
- `font`: Primary font size for the digits.
- `digitSpacing`: Horizontal spacing between cards inside one row.
- `rowSpacing`: Vertical spacing between rows (portrait only).
- `outerPadding`: Horizontal padding that should wrap the row stack.
- `innerPadding`: Supplemental padding applied inside a row/card stack.
- `scale`: Optional multiplicative factor used by the legacy landscape view to
  scale the stack after layout (defaults to `1`).

## Portrait Algorithm

1. **Initial width guess** — Assume every row holds `columnsPerRow` cards
   (`columnsPerRow = 2`). Compute the raw card width by removing the outer
   padding and minimum column spacing from the available width.
   ```
   rawWidth = (contentWidth - 2*edgePadding - (columnsPerRow-1)*minColumnSpacing)
              / columnsPerRow
   ```
2. **Card height** — Multiply by the aspect ratio to obtain `rawHeight`.
3. **Derived spacing** — Row spacing uses `max(rawWidth * 0.18, minRowSpacing)`.
   Horizontal spacing and internal padding are proportional to `rawWidth` with
   hard minimums to stop the UI from collapsing on very small screens.
4. **Height check** — Compute the total height required:
   ```
   requiredHeight = rows * rawHeight + (rows - 1) * rowSpacing
                    + 2 * innerVerticalPadding
   ```
   If `requiredHeight` exceeds the available height, apply a uniform scale to
   all horizontal metrics so that it fits exactly.
5. **Width check** — Re-run step 1 using the scaled card width to confirm the
   outer padding constraint. If the constraint fails (e.g. very narrow device),
   apply a second uniform scale to keep the layout inside the bounds.
6. **Font & padding** — Font size is `cardWidth * 0.78`. Inner padding values
   are taken as small percentages of the card width/height.

This algorithm keeps the Flip Clock centred and balanced across phone sizes.
When seconds are disabled (`rows = 2`), the same maths yields larger cards.

## Landscape Algorithm

1. **Initial width** — Use the entire `contentWidth` to compute a raw card
   width:
   ```
   rawWidth = (contentWidth - 2*edgePadding - (columns-1)*minColumnSpacing)
              / columns
   ```
2. **Controls column** — When the caller requests a side control rail
   (Pomodoro in landscape), split the horizontal budget into two sections:
   the flip-card deck and a fixed-width column that stacks the buttons. The
   column width equals `buttonSize + 2 * edgePadding` and shares the same
   vertical centre as the deck.
3. **Maximum width cap** — Clamp the card width using an upper bound such as
   `contentWidth * 0.18` to keep four-column layouts from becoming oversized.
   A lower bound ensures readability on narrow split-screen windows.
4. **Height budget** — Derive `maxCardHeight = contentHeight - extraVerticalPadding`
   and shrink `rawWidth` proportionally if the computed height would overflow.
   When a control rail is present, enforce that the card height is at least the
   vertical button stack height so the controls stay inside the frame.
5. **Final spacing** — Horizontal spacing scales with card width but never drops
   below `16pt`. Outer padding is recalculated so the deck remains centred in
   the remaining space, and the control column fills the opposite side. The row
   spacing remains `0` for single-row layouts.
6. **Font & padding** — Same derivations as portrait ensure consistent corner
   radii, inner padding and typography. Control buttons reuse the calculated
   card height when aligning to the deck.

With the control rail carved out explicitly, the digit deck no longer competes
with the button row for vertical space, preventing the POMODORO header from
being pushed outside the safe area.

## Implementation Notes

- The calculators should live in a reusable helper for both `FlipClockView` and
  `PomodoroPageView` to consume. Portrait mode uses `rows = showsSeconds ? 3 : 2`;
  landscape uses `rows = 1`, `columns = showsSeconds ? 6 : 4` (clock) or `4`
  (Pomodoro).
- Safe-area insets and the top/bottom button clusters must be removed before
  passing the size to the calculators.
- All derived values should be deterministic from the inputs so the UI stays in
  sync during animations.

## Validation Checklist

When adjusting constants, run through this grid and confirm no clipping occurs:

| Device              | Orientation | Seconds | Mode       | Expected Result |
|---------------------|-------------|---------|------------|-----------------|
| iPhone 16 Pro       | Portrait    | On      | Clock      | 3 rows centred, no overlap |
| iPhone 16 Pro       | Portrait    | Off     | Clock      | 2 rows larger cards |
| iPhone 16 Pro       | Landscape   | On      | Clock      | 1 row, readable width |
| iPhone 16 Pro       | Landscape   | Off     | Pomodoro   | Cards capped, controls visible |
| iPad Pro 13"        | Both        | On/Off  | Clock/Pomo | Cards scale but stay within bounds |
| iPhone SE (Compact) | Portrait    | On      | Clock      | Layout scales down without truncation |

Document any deviations from these expectations before shipping.
