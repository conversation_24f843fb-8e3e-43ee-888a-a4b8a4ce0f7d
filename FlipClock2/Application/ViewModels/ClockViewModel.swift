import Combine
import Foundation
import Swift<PERSON>

@MainActor
final class ClockViewModel: ObservableObject {
    @Published private(set) var snapshot: ClockSnapshot = .placeholder

    private let engine: ClockEngine
    private var streamTask: Task<Void, Never>?

    init(engine: ClockEngine) {
        self.engine = engine
    }

    convenience init() {
        self.init(engine: DisplayLinkClockEngine())
    }

    func start() {
        guard streamTask == nil else { return }
        engine.start()
        streamTask = Task { [weak self] in
            guard let self else { return }
            for await snapshot in engine.tickStream {
                self.snapshot = snapshot
            }
        }
    }

    func stop() {
        streamTask?.cancel()
        streamTask = nil
        engine.stop()
    }

    deinit {
        Task { @MainActor [weak self] in
            self?.stop()
        }
    }
}
