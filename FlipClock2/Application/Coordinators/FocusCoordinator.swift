import Foundation
import Combine

@MainActor
final class FocusCoordinator: ObservableObject {
    private let service: FocusService
    private let defaultPreset: FocusPreset

    init(service: FocusService, defaultPreset: FocusPreset) {
        self.service = service
        self.defaultPreset = defaultPreset
    }

    var focusService: FocusService { service }

    func toggle() {
        if !service.isRunning {
            service.start(with: defaultPreset)
            return
        }
        if service.snapshot.isPaused {
            service.resume()
        } else {
            service.pause()
        }
    }

    func reset() {
        service.stop()
    }

    func skipPhase() {
        service.skipPhase()
    }
}
