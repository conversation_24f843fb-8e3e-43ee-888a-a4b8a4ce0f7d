#if canImport(ActivityKit)
import Foundation
import ActivityKit
import Combine

struct FocusActivityAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        var phase: String
        var endDate: Date
        var isPaused: Bool
    }

    var title: String
}

@MainActor
final class FocusLiveActivityCoordinator {
    static let shared = FocusLiveActivityCoordinator()

    private var cancellable: AnyCancellable?
    private var activity: Activity<FocusActivityAttributes>?

    private init() {}

    func observe(service: FocusService) {
        cancellable?.cancel()
        cancellable = service.$snapshot
            .receive(on: RunLoop.main)
            .sink { [weak self, weak service] snapshot in
                guard let self, let service else { return }
                self.handle(snapshot: snapshot, isRunning: service.isRunning)
            }
    }

    private func handle(snapshot: FocusTickerSnapshot, isRunning: Bool) {
        guard ActivityAuthorizationInfo().areActivitiesEnabled else { return }
        if isRunning && !snapshot.isPaused {
            updateActivity(with: snapshot)
        } else if isRunning && snapshot.isPaused {
            updateActivity(with: snapshot)
        } else {
            endActivity()
        }
    }

    private func updateActivity(with snapshot: FocusTickerSnapshot) {
        let endDate = Date().addingTimeInterval(snapshot.remaining)
        let state = FocusActivityAttributes.ContentState(phase: snapshot.phase.displayName,
                                                         endDate: endDate,
                                                         isPaused: snapshot.isPaused)
        let content = ActivityContent(state: state, staleDate: endDate)
        if let activity {
            Task {
                await activity.update(content)
            }
        } else {
            let attributes = FocusActivityAttributes(title: "番茄钟")
            Task {
                do {
                    activity = try Activity<FocusActivityAttributes>.request(attributes: attributes, content: content)
                } catch {
                    print("[FocusLiveActivity] request failed: \(error)")
                }
            }
        }
    }

    private func endActivity() {
        guard let activity else { return }
        Task {
            await activity.end(nil, dismissalPolicy: .immediate)
            self.activity = nil
        }
    }
}
#endif
