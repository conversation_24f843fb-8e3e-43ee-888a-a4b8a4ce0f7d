import SwiftUI

struct WhiteNoisePanelView: View {
    @ObservedObject var service: WhiteNoiseService

    private let profiles = WhiteNoiseProfile.allCases

    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            header
            profileButtons
            volumeSlider
            controlButtons
            tips
        }
        .padding(24)
        .frame(maxWidth: .infinity)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 28, style: .continuous))
    }

    private var header: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("白噪音")
                .font(.title2.weight(.semibold))
            Text("轻松混入环境声，帮助进入专注或放松状态")
                .font(.footnote)
                .foregroundStyle(.secondary)
        }
    }

    private var profileButtons: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("声音场景")
                .font(.subheadline.weight(.semibold))
            HStack(spacing: 12) {
                ForEach(profiles) { profile in
                    Button {
                        if service.currentProfile == profile, service.isPlaying {
                            service.stop()
                        } else {
                            service.play(profile: profile)
                        }
                    } label: {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(profile.displayName)
                                .font(.subheadline.weight(.medium))
                            Text(description(for: profile))
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .frame(maxWidth: .infinity)
                        .background(service.currentProfile == profile && service.isPlaying ? Color.accentColor.opacity(0.18) : Color.secondary.opacity(0.08))
                        .overlay(
                            RoundedRectangle(cornerRadius: 18, style: .continuous)
                                .stroke(service.currentProfile == profile && service.isPlaying ? Color.accentColor.opacity(0.45) : Color.secondary.opacity(0.2), lineWidth: 1)
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 18, style: .continuous))
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }

    private var volumeSlider: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("音量")
                .font(.subheadline.weight(.semibold))
            Slider(value: Binding(get: {
                Double(service.volume)
            }, set: { service.volume = Float($0) }), in: 0...1)
            Text("当前音量：\(Int(service.volume * 100))%")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
    }

    private var controlButtons: some View {
        HStack(spacing: 16) {
            if service.isPlaying {
                Button(role: .destructive) {
                    service.stop()
                } label: {
                    Label("停止播放", systemImage: "stop.fill")
                }
                .buttonStyle(.bordered)
            }
        }
    }

    private var tips: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("温馨提示")
                .font(.footnote.weight(.semibold))
            Text("白噪音可与其他音乐同时播放，建议配合系统“勿扰模式”使用。")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
    }

    private func description(for profile: WhiteNoiseProfile) -> String {
        switch profile {
        case .rainfall: return "柔和雨声，与低频风声混合"
        case .cafe: return "咖啡馆氛围，轻语声与背景乐"
        case .brown: return "低频棕噪，舒缓持续"
        }
    }
}
