import SwiftUI

struct ContentView: View {
    @StateObject private var viewModel: ClockViewModel
    @StateObject private var themeStore: ThemeStore
    @Environment(\.scenePhase) private var scenePhase
    @State private var showsSettings = false
    @State private var showsFocusPanel = false
    @StateObject private var focusCoordinator: FocusCoordinator
    private let defaultPomodoroPreset: FocusPreset
    @State private var showsPomodoro = false

    init(container: DependencyContainer = .shared) {
        let preset = FocusPreset(name: "经典", focusDuration: 25 * 60, restDuration: 5 * 60)
        self.defaultPomodoroPreset = preset
        _viewModel = StateObject(wrappedValue: container.makeClockViewModel())
        _themeStore = StateObject(wrappedValue: container.makeThemeStore())
        let service = container.makeFocusService()
        _focusCoordinator = StateObject(wrappedValue: FocusCoordinator(service: service, defaultPreset: preset))
    }

    private var focusService: FocusService {
        focusCoordinator.focusService
    }

    var body: some View {
        GeometryReader { proxy in
            let theme = themeStore.selectedTheme
            let spec = themeStore.glassSpec(for: theme)
            let safeInsets = proxy.safeAreaInsets
            let layoutInsets = contentInsets(for: proxy.size, safeInsets: safeInsets)

            ZStack {
                theme.background
                    .ignoresSafeArea()
                    .overlay(
                        LinearGradient(
                            colors: [theme.background.opacity(0.62), theme.background.opacity(0.96)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                ModeFlipView(isFlipped: showsPomodoro) {
                    FlipClockView(
                        snapshot: viewModel.snapshot,
                        theme: theme,
                        spec: spec,
                        mode: themeStore.mode,
                        showsSeconds: true
                    )
                } back: {
                    PomodoroPageView(
                        focusService: focusService,
                        defaultPreset: defaultPomodoroPreset,
                        theme: theme,
                        spec: spec,
                        onSettings: { showsFocusPanel = true },
                        onPrimary: { toggleFocus() },
                        onReset: { focusCoordinator.reset() }
                    )
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(.top, layoutInsets.top)
                .padding(.bottom, layoutInsets.bottom)

                topBar(theme: theme, spec: spec, safeInsets: safeInsets)
            }
        }
        .onAppear {
            viewModel.start()
            #if canImport(ActivityKit)
            FocusLiveActivityCoordinator.shared.observe(service: focusService)
            #endif
        }
        .onChangeCompat(of: scenePhase) { phase in
            switch phase {
            case .active:
                viewModel.start()
            case .background, .inactive:
                viewModel.stop()
            @unknown default:
                break
            }
        }
        .sheet(isPresented: $showsSettings) {
            SettingsView(themeStore: themeStore)
                .presentationDetents([.medium, .large])
        }
        .sheet(isPresented: $showsFocusPanel) {
            FocusPanelView(service: focusService)
                .presentationDetents([.fraction(0.55), .medium, .large])
        }
    }
    private func topBar(theme: Theme, spec: GlassSpec, safeInsets: EdgeInsets) -> some View {
        VStack(spacing: 0) {
            VStack(spacing: showsPomodoro ? 0 : 12) {
                HStack {
                    modeSwitchButton(theme: theme, spec: spec)
                        .padding(.leading, safeInsets.leading + 20)
                    Spacer()
                    settingsButton(theme: theme, spec: spec)
                        .padding(.trailing, safeInsets.trailing + 20)
                }
                .padding(.top, safeInsets.top + 4)

                if !showsPomodoro {
                    Text("FLIPCLOCK")
                        .font(.system(size: 20, weight: .semibold, design: .rounded))
                        .tracking(2)
                        .foregroundStyle(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
            }
            Spacer()
        }
    }

    private func settingsButton(theme: Theme, spec: GlassSpec) -> some View {
        Button {
            showsSettings = true
        } label: {
            HStack(spacing: 8) {
                Image(systemName: "slider.horizontal.3")
                Text("设置")
                    .font(.subheadline.weight(.semibold))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .foregroundStyle(theme.control.foreground.opacity(0.92))
            .glassBackground(spec: spec, theme: theme)
        }
        .buttonStyle(.plain)
    }

    private func modeSwitchButton(theme: Theme, spec: GlassSpec) -> some View {
        Button {
            withAnimation(.timingCurve(0.42, 0.2, 0.1, 0.9, duration: 0.56)) {
                showsPomodoro.toggle()
            }
        } label: {
            HStack(spacing: 8) {
                Image(systemName: showsPomodoro ? "clock" : "timer")
                Text(showsPomodoro ? "Clock" : "Pomodoro")
                    .font(.subheadline.weight(.semibold))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .foregroundStyle(theme.control.foreground.opacity(0.92))
            .glassBackground(spec: spec, theme: theme)
        }
        .buttonStyle(.plain)
    }

    private func contentInsets(for size: CGSize, safeInsets: EdgeInsets) -> (top: CGFloat, bottom: CGFloat) {
        let controlBarHeight: CGFloat = 44
        let titleHeight: CGFloat = showsPomodoro ? 0 : 24
        let headerSpacing: CGFloat = showsPomodoro ? 8 : 12
        let topBarHeight: CGFloat = controlBarHeight + titleHeight + headerSpacing
        let topSpacing = safeInsets.top + topBarHeight
        let baseBottomSpacing = safeInsets.bottom + 16

        guard showsPomodoro else {
            let balanced = max(topSpacing, baseBottomSpacing)
            return (top: balanced, bottom: balanced)
        }

        let pomodoroControlHeight: CGFloat = 74
        let controlSpacing: CGFloat = (size.width > size.height) ? 24 : 32
        let pomodoroBottom = safeInsets.bottom + 16 + controlSpacing + pomodoroControlHeight
        let adjustedTop = max(topSpacing, baseBottomSpacing)
        return (top: adjustedTop, bottom: pomodoroBottom)
    }

    private func toggleFocus() {
        focusCoordinator.toggle()
    }

    private struct ModeFlipView<Front: View, Back: View>: View {
        let isFlipped: Bool
        let duration: Double
        let perspective: CGFloat
        let front: Front
        let back: Back

        init(isFlipped: Bool,
             duration: Double = 0.56,
             perspective: CGFloat = 0.68,
             @ViewBuilder front: () -> Front,
             @ViewBuilder back: () -> Back) {
            self.isFlipped = isFlipped
            self.duration = duration
            self.perspective = perspective
            self.front = front()
            self.back = back()
        }

        var body: some View {
            ZStack {
                front
                    .rotation3DEffect(
                        .degrees(isFlipped ? 180 : 0),
                        axis: (x: 0, y: 1, z: 0),
                        perspective: perspective
                    )
                    .backfaceHidden()
                    .opacity(isFlipped ? 0 : 1)
                    .allowsHitTesting(!isFlipped)

                back
                    .rotation3DEffect(
                        .degrees(isFlipped ? 0 : -180),
                        axis: (x: 0, y: 1, z: 0),
                        perspective: perspective
                    )
                    .backfaceHidden()
                    .opacity(isFlipped ? 1 : 0)
                    .allowsHitTesting(isFlipped)
            }
            .animation(
                .timingCurve(0.42, 0.2, 0.1, 0.9, duration: duration),
                value: isFlipped
            )
        }
    }
}

private extension View {
    func backfaceHidden() -> some View {
        modifier(DoubleSidedHiddenModifier())
    }
}

private struct DoubleSidedHiddenModifier: ViewModifier {
    func body(content: Content) -> some View {
        #if canImport(UIKit)
        content.background(DoubleSidedDisablingView())
        #else
        content
        #endif
    }

    #if canImport(UIKit)
    private struct DoubleSidedDisablingView: UIViewRepresentable {
        func makeUIView(context: Context) -> UIView {
            let view = UIView(frame: .zero)
            view.isUserInteractionEnabled = false
            view.backgroundColor = .clear
            return view
        }

        func updateUIView(_ uiView: UIView, context: Context) {
            DispatchQueue.main.async {
                uiView.superview?.layer.isDoubleSided = false
                uiView.superview?.superview?.layer.isDoubleSided = false
            }
        }
    }
    #endif
}

#Preview {
    ContentView()
}
