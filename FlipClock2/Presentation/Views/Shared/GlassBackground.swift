import SwiftUI

struct GlassBackground: ViewModifier {
    let spec: GlassSpec
    let theme: Theme

    func body(content: Content) -> some View {
        content
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 22, style: .continuous)
                        .fill(theme.control.base)
                        .blur(radius: spec.blur)
                    RoundedRectangle(cornerRadius: 22, style: .continuous)
                        .fill(theme.control.highlight.opacity(spec.highlightOpacity))
                        .blendMode(.screen)
                        .padding(.vertical, 1)
                    RoundedRectangle(cornerRadius: 22, style: .continuous)
                        .stroke(theme.control.border.opacity(spec.borderOpacity), lineWidth: 1)
                }
                .opacity(spec.opacity)
                .shadow(color: theme.control.shadow.opacity(0.4), radius: 18, y: 8)
            )
    }
}

extension View {
    func glassBackground(spec: GlassSpec, theme: Theme) -> some View {
        modifier(GlassBackground(spec: spec, theme: theme))
    }
}
