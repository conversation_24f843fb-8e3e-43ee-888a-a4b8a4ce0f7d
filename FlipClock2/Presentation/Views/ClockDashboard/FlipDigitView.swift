import SwiftUI
#if os(iOS)
import UIKit
#endif

/// 单个数字翻页卡片：上半片与下半片分阶段动画，保证翻页时序自然。
struct FlipDigitView: View {
    let digit: Int
    let theme: Theme
    let spec: GlassSpec
    let font: Font
    let animationDuration: Double

    @State private var currentDigit: Int
    @State private var lowerStaticDigit: Int
    @State private var upcomingDigit: Int

    @State private var topAngle: Double = 0
    @State private var bottomAngle: Double = 90
    @State private var isTopAnimating = false
    @State private var isBottomAnimating = false
    @State private var isAnimating = false

    @State private var queuedDigit: Int?

    init(digit: Int, theme: Theme, spec: GlassSpec, font: Font, animationDuration: Double = 0.45) {
        self.digit = digit
        self.theme = theme
        self.spec = spec
        self.font = font
        self.animationDuration = animationDuration
        _currentDigit = State(initialValue: digit)
        _lowerStaticDigit = State(initialValue: digit)
        _upcomingDigit = State(initialValue: digit)
    }

    var body: some View {
        GeometryReader { geo in
            let width = geo.size.width
            let height = geo.size.height
            let halfHeight = height / 2
            let corner = height * 0.16

            ZStack {
                cardBackground(corner: corner)
                    .overlay(alignment: .center) { divider(width: width) }

                // 静态上半片
                digitHalf(digit: currentDigit, width: width, halfHeight: halfHeight, position: .top)
                    .opacity(isTopAnimating ? 0 : 1)

                // 静态下半片（在下半动画阶段保持旧数字）
                digitHalf(digit: lowerStaticDigit, width: width, halfHeight: halfHeight, position: .bottom)
                    .opacity(isBottomAnimating ? 0 : 1)

                // 上半动画片（旧数字翻起）
                digitHalf(digit: currentDigit, width: width, halfHeight: halfHeight, position: .top)
                    .rotation3DEffect(.degrees(topAngle), axis: (x: 1, y: 0, z: 0), anchor: .bottom, perspective: 0.7)
                    .opacity(isTopAnimating ? 1 : 0)
                    .shadow(color: .black.opacity(0.35), radius: 8, x: 0, y: 4)

                // 下半动画片（展示新数字）
                digitHalf(digit: upcomingDigit, width: width, halfHeight: halfHeight, position: .bottom)
                    .rotation3DEffect(.degrees(bottomAngle), axis: (x: 1, y: 0, z: 0), anchor: .top, perspective: 0.7)
                    .opacity(isBottomAnimating ? 1 : 0)
                    .shadow(color: .black.opacity(0.25), radius: 6, x: 0, y: 6)
            }
            .clipShape(RoundedRectangle(cornerRadius: corner, style: .continuous))
            .overlay(
                RoundedRectangle(cornerRadius: corner, style: .continuous)
                    .stroke(Color.white.opacity(0.08), lineWidth: 0.8)
            )
            .overlay(splitDivider)
            .onAppear { reset(to: digit) }
            .onChangeCompat(of: digit) { newValue in scheduleFlip(to: newValue) }
        }
    }

    private enum HalfPosition { case top, bottom }

    private func digitHalf(digit: Int, width: CGFloat, halfHeight: CGFloat, position: HalfPosition) -> some View {
        let alignment: Alignment = position == .top ? .top : .bottom

        return Text(String(digit))
            .font(font)
            .monospacedDigit()
            .kerning(-2)
            .foregroundStyle(adjustedDigitColor())
            .frame(width: width, height: halfHeight * 2, alignment: .center)
            .mask(alignment: alignment) {
                Rectangle()
                    .frame(width: width, height: halfHeight)
            }
            .frame(width: width, height: halfHeight, alignment: .center)
            .overlay(alignment: alignment) {
                gradientOverlay(for: position)
                    .frame(height: halfHeight * 0.95)
            }
    }

    private func gradientOverlay(for position: HalfPosition) -> some View {
        switch position {
        case .top:
            return LinearGradient(
                colors: [Color.white.opacity(0.35), Color.black.opacity(0.12)],
                startPoint: .top,
                endPoint: .bottom
            )
            .opacity(0.95)
        case .bottom:
            return LinearGradient(
                colors: [Color.black.opacity(0.65), Color.black.opacity(0.2)],
                startPoint: .top,
                endPoint: .bottom
            )
            .opacity(0.98)
        }
    }

    private func cardBackground(corner: CGFloat) -> some View {
        let topColor = Color.black.opacity(0.86)
        let bottomColor = Color.black.opacity(0.66)
        let highlight = Color.white.opacity(0.06 + spec.highlightOpacity * 0.25)
        return RoundedRectangle(cornerRadius: corner, style: .continuous)
            .fill(LinearGradient(colors: [topColor, bottomColor], startPoint: .top, endPoint: .bottom))
            .overlay(
                RoundedRectangle(cornerRadius: corner, style: .continuous)
                    .stroke(highlight, lineWidth: 0.6)
            )
    }

    private func divider(width: CGFloat) -> some View {
        VStack(spacing: 0) {
            Color.white.opacity(0.14).frame(height: 0.6)
            Color.black.opacity(0.55).frame(height: 0.9)
        }
        .frame(width: width)
    }

    private func reset(to value: Int) {
        currentDigit = value
        lowerStaticDigit = value
        upcomingDigit = value
        topAngle = 0
        bottomAngle = 90
        isAnimating = false
        isTopAnimating = false
        isBottomAnimating = false
        queuedDigit = nil
    }

    private func scheduleFlip(to newDigit: Int) {
        let sanitized = (newDigit + 10) % 10
        guard sanitized != currentDigit else { return }

        if isAnimating {
            queuedDigit = sanitized
            return
        }

        isAnimating = true
        isTopAnimating = true
        isBottomAnimating = false
        queuedDigit = nil
        upcomingDigit = sanitized

        withAnimation(.easeIn(duration: animationDuration / 2)) {
            topAngle = -90
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + animationDuration / 2) {
            completeTopAnimation(with: sanitized)
        }
    }

    private func completeTopAnimation(with digit: Int) {
        currentDigit = digit
        topAngle = 0
        bottomAngle = 90
        isTopAnimating = false
        isBottomAnimating = true

        withAnimation(.easeOut(duration: animationDuration / 2)) {
            bottomAngle = 0
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + animationDuration / 2) {
            finishAnimation()
        }
    }

    private func finishAnimation() {
        lowerStaticDigit = currentDigit
        isBottomAnimating = false
        isAnimating = false

        if let next = queuedDigit {
            queuedDigit = nil
            scheduleFlip(to: next)
        }
    }

#if os(iOS)
private var isHighContrastEnabled: Bool {
    UIAccessibility.isDarkerSystemColorsEnabled
}
#else
private var isHighContrastEnabled: Bool { false }
#endif
    private var splitDivider: some View {
        Rectangle()
            .fill(
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.65),
                        Color.white.opacity(0.45),
                        Color.black.opacity(0.65)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .frame(height: 1.6)
            .opacity(0.9)
            .blur(radius: 0.15)
    }

    private func adjustedDigitColor() -> Color {
#if os(iOS)
        let base = UIColor(theme.control.digitHighContrast)
        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0
        if base.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha) {
            if brightness < 0.75 { brightness = min(1.0, brightness + 0.25) }
            if saturation < 0.25 { saturation = min(1.0, saturation + 0.15) }
            return Color(UIColor(hue: hue, saturation: saturation, brightness: brightness, alpha: alpha))
        }
        if let components = base.cgColor.components, components.count >= 3 {
            let r = components[0], g = components[1], b = components[2]
            let luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b
            if luminance < 0.7 {
                let adjusted = UIColor(red: min(r + 0.25, 1), green: min(g + 0.25, 1), blue: min(b + 0.25, 1), alpha: base.cgColor.alpha)
                return Color(adjusted)
            }
        }
        return Color(base)
#else
        return theme.digitColor
#endif
    }
}
