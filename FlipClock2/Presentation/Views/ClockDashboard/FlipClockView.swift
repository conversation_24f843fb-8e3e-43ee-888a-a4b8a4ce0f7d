import SwiftUI

struct FlipClockView: View {
    let snapshot: ClockSnapshot
    let theme: Theme
    let spec: GlassSpec
    let mode: VisualMode
    let showsSeconds: Bool

    var body: some View {
        GeometryReader { geo in
            let digits = formattedDigits(from: snapshot.date)
            let isLandscape = geo.size.width >= geo.size.height
            let columns = showsSeconds ? 6 : 4
            let rows = showsSeconds ? 3 : 2
            let cardAspectRatio: CGFloat = 1.32
            let edgePadding: CGFloat = 16
            let minColumnSpacing: CGFloat = 16
            let minRowSpacing: CGFloat = 12

            if isLandscape {
                let input = LayoutInput(
                    contentSize: geo.size,
                    columns: columns,
                    rows: 1,
                    edgePadding: edgePadding,
                    aspectRatio: cardAspectRatio,
                    minColumnSpacing: minColumnSpacing,
                    minRowSpacing: minRowSpacing,
                    extraVerticalPadding: 0,
                    controlColumnWidth: 0,
                    controlColumnHeight: 0
                )
                LandscapeClock(
                    digits: digits,
                    theme: theme,
                    spec: spec,
                    mode: mode,
                    showsSeconds: showsSeconds,
                    metrics: LayoutMetricsBuilder.metrics(for: .landscape, input: input)
                )
            } else {
                let input = LayoutInput(
                    contentSize: geo.size,
                    columns: 2,
                    rows: rows,
                    edgePadding: edgePadding,
                    aspectRatio: cardAspectRatio,
                    minColumnSpacing: minColumnSpacing,
                    minRowSpacing: minRowSpacing,
                    extraVerticalPadding: 0,
                    controlColumnWidth: 0,
                    controlColumnHeight: 0
                )
                PortraitClock(
                    digits: digits,
                    theme: theme,
                    spec: spec,
                    mode: mode,
                    showsSeconds: showsSeconds,
                    metrics: LayoutMetricsBuilder.metrics(for: .portrait, input: input)
                )
            }
        }
    }

    private func formattedDigits(from date: Date) -> (hours: [Int], minutes: [Int], seconds: [Int]) {
        let calendar = Calendar.autoupdatingCurrent
        let comps = calendar.dateComponents([.hour, .minute, .second], from: date)
        let hour = comps.hour ?? 0
        let minute = comps.minute ?? 0
        let second = comps.second ?? 0

        func digits(for value: Int) -> [Int] {
            String(format: "%02d", value).compactMap { Int(String($0)) }
        }

        return (digits(for: hour), digits(for: minute), digits(for: second))
    }
}

// MARK: - Landscape Layout

private struct LandscapeClock: View {
    let digits: (hours: [Int], minutes: [Int], seconds: [Int])
    let theme: Theme
    let spec: GlassSpec
    let mode: VisualMode
    let showsSeconds: Bool
    let metrics: ClockLayoutMetrics

    var body: some View {
        VStack {
            Spacer()
            HStack(spacing: metrics.digitSpacing) {
                digitGroup(values: digits.hours)
                digitGroup(values: digits.minutes)
                if showsSeconds {
                    digitGroup(values: digits.seconds)
                }
            }
            .padding(.horizontal, metrics.outerPadding)
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private func digitGroup(values: [Int]) -> some View {
        HStack(spacing: metrics.digitSpacing) {
            ForEach(Array(values.enumerated()), id: \.offset) { _, value in
                FlipDigitView(digit: value, theme: theme, spec: spec, font: metrics.font)
                    .frame(width: metrics.cardSize.width, height: metrics.cardSize.height)
            }
        }
        .padding(.horizontal, metrics.innerPadding)
    }
}

// MARK: - Portrait Layout

private struct PortraitClock: View {
    let digits: (hours: [Int], minutes: [Int], seconds: [Int])
    let theme: Theme
    let spec: GlassSpec
    let mode: VisualMode
    let showsSeconds: Bool
    let metrics: ClockLayoutMetrics

    var body: some View {
        VStack {
            Spacer()
            VStack(spacing: metrics.rowSpacing) {
                digitStack(values: digits.hours)
                digitStack(values: digits.minutes)
                if showsSeconds {
                    digitStack(values: digits.seconds)
                }
            }
            .padding(.horizontal, metrics.outerPadding)
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private func digitStack(values: [Int]) -> some View {
        HStack(spacing: metrics.digitSpacing) {
            ForEach(Array(values.enumerated()), id: \.offset) { _, value in
                FlipDigitView(digit: value, theme: theme, spec: spec, font: metrics.font)
                    .frame(width: metrics.cardSize.width, height: metrics.cardSize.height)
            }
        }
        .padding(.horizontal, metrics.innerPadding)
        .padding(.vertical, metrics.innerPaddingVertical)
    }
}
