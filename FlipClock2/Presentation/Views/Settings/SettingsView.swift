import SwiftUI

struct SettingsView: View {
    @ObservedObject var themeStore: ThemeStore
    @Environment(\.dismiss) private var dismiss

    private var theme: Theme { themeStore.selectedTheme }
    private var spec: GlassSpec { themeStore.glassSpec(for: theme) }

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 32) {
                    header
                    visualModeSection
                    themeSection
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 32)
            }
            .background(
                LinearGradient(
                    colors: [theme.background.opacity(0.12), theme.background.opacity(0.3)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            )
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("完成") { dismiss() }
                }
            }
        }
    }

    private var header: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("翻页时钟外观")
                .font(.title3.weight(.semibold))
                .foregroundStyle(theme.digitColor.opacity(0.9))
            Text("即时预览视觉模式与主题，调出适合你当前环境的质感。")
                .font(.footnote)
                .foregroundStyle(theme.digitColor.opacity(0.55))
        }
    }

    private var visualModeSection: some View {
        VStack(alignment: .leading, spacing: 18) {
            sectionHeader(title: "视觉模式", subtitle: "快速切换标准 / 舒适 / 纯色三档材质")

            Picker("视觉模式", selection: $themeStore.mode) {
                ForEach(VisualMode.allCases) { mode in
                    Text(mode.label).tag(mode)
                }
            }
            .pickerStyle(.segmented)
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 28)
        .glassBackground(spec: spec, theme: theme)
    }

    private var themeSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            sectionHeader(title: "主题", subtitle: "挑选不同气质的配色，付费主题将实时预览")

            LazyVGrid(columns: [GridItem(.adaptive(minimum: 150), spacing: 18)], spacing: 20) {
                ForEach(themeStore.themes) { item in
                    ThemeCard(theme: item, isSelected: item.id == themeStore.selectedTheme.id)
                        .onTapGesture {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.85)) {
                                themeStore.applyTheme(item)
                            }
                        }
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 28)
        .glassBackground(spec: spec, theme: theme)
    }

    private func sectionHeader(title: String, subtitle: String) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.headline)
                .foregroundStyle(theme.digitColor.opacity(0.88))
            Text(subtitle)
                .font(.caption)
                .foregroundStyle(theme.digitColor.opacity(0.58))
        }
    }
}

private struct ThemeCard: View {
    let theme: Theme
    let isSelected: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            RoundedRectangle(cornerRadius: 18, style: .continuous)
                .fill(theme.background)
                .frame(height: 96)
                .overlay(
                    LinearGradient(colors: [theme.accent.opacity(0.22), theme.accent.opacity(0.08)], startPoint: .topLeading, endPoint: .bottomTrailing)
                        .clipShape(RoundedRectangle(cornerRadius: 18, style: .continuous))
                )
            Text(theme.name)
                .font(.subheadline.weight(.semibold))
                .foregroundStyle(theme.digitColor.opacity(0.86))
                .lineLimit(1)
        }
        .padding(16)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: 22, style: .continuous)
                .fill(Color.white.opacity(isSelected ? 0.22 : 0.08))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 22, style: .continuous)
                .stroke(Color.white.opacity(isSelected ? 0.48 : 0.18), lineWidth: 1.2)
        )
        .scaleEffect(isSelected ? 1.03 : 1.0)
        .animation(.spring(response: 0.45, dampingFraction: 0.9), value: isSelected)
    }
}
