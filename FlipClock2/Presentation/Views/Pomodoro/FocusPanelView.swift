import SwiftUI

struct FocusPanelView: View {
    @ObservedObject var service: FocusService
    @State private var selectedPresetIndex: Int = 0
    @State private var customPresets: [FocusPreset] = [
        FocusPreset(name: "自定义 A", focusDuration: 20 * 60, restDuration: 5 * 60),
        FocusPreset(name: "自定义 B", focusDuration: 45 * 60, restDuration: 10 * 60)
    ]

    private var presets: [FocusPreset] {
        let defaults = [
            FocusPreset(name: "经典", focusDuration: 25 * 60, restDuration: 5 * 60),
            FocusPreset(name: "深度", focusDuration: 45 * 60, restDuration: 10 * 60),
            FocusPreset(name: "冲刺", focusDuration: 15 * 60, restDuration: 3 * 60)
        ]
        return defaults + customPresets
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            header
            presetPicker
            progressSection
            controlButtons
            customEditors
        }
        .padding(24)
        .frame(maxWidth: .infinity)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 28, style: .continuous))
    }

    private var header: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("番茄钟")
                .font(.title2.weight(.semibold))
            Text("快速切换不同节奏，专注与休息交替进行")
                .font(.footnote)
                .foregroundStyle(.secondary)
        }
    }

    private var presetPicker: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("预设节奏")
                .font(.subheadline.weight(.semibold))
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(Array(presets.enumerated()), id: \.offset) { index, preset in
                        presetButton(preset, index: index)
                    }
                }
            }
        }
    }

    private func presetButton(_ preset: FocusPreset, index: Int) -> some View {
        Button {
            selectedPresetIndex = index
            if service.isRunning {
                service.stop()
            }
            service.start(with: preset)
        } label: {
            VStack(alignment: .leading, spacing: 4) {
                Text(preset.name)
                    .font(.subheadline.weight(.medium))
                    .foregroundStyle(.primary)
                Text(preset.description)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(selectedPresetIndex == index ? Color.accentColor.opacity(0.18) : Color.secondary.opacity(0.08))
            .overlay(
                RoundedRectangle(cornerRadius: 16, style: .continuous)
                    .stroke(selectedPresetIndex == index ? Color.accentColor.opacity(0.45) : Color.secondary.opacity(0.2), lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
        }
        .buttonStyle(.plain)
    }

    private var progressSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("阶段进度")
                .font(.subheadline.weight(.semibold))
            ProgressView(value: service.snapshot.total == 0 ? 0 : (1 - service.snapshot.remaining / service.snapshot.total))
                .progressViewStyle(.linear)
            HStack {
                Text(service.snapshot.phase.displayName)
                Spacer()
                Text("第 \(max(1, service.snapshot.cycle)) 轮")
                Text("剩余 \(formattedTime(service.snapshot.remaining))")
            }
            .font(.footnote)
            .foregroundStyle(.secondary)
        }
    }

    private var controlButtons: some View {
        HStack(spacing: 16) {
            Button(action: toggleRun) {
                Label(service.snapshot.isPaused || !service.isRunning ? "开始" : "暂停", systemImage: service.snapshot.isPaused || !service.isRunning ? "play.fill" : "pause.fill")
            }
            .buttonStyle(.borderedProminent)

            Button(role: .destructive) {
                service.stop()
            } label: {
                Label("停止", systemImage: "stop.fill")
            }
            .buttonStyle(.bordered)

            if service.isRunning {
                Button {
                    service.skipPhase()
                } label: {
                    Label("跳过", systemImage: "forward.end.fill")
                }
                .buttonStyle(.bordered)
            }
        }
    }

    private var customEditors: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("自定义预设")
                .font(.subheadline.weight(.semibold))
            ForEach(Array(customPresets.enumerated()), id: \.offset) { index, preset in
                VStack(alignment: .leading, spacing: 8) {
                    Text(preset.name)
                        .font(.footnote.weight(.medium))
                    HStack {
                        Stepper(value: binding(for: index, keyPath: \.focusDuration), in: 5 * 60 ... 120 * 60, step: 60) {
                            Text("专注 \(Int(customPresets[index].focusDuration / 60)) 分钟")
                                .font(.caption)
                        }
                        Stepper(value: binding(for: index, keyPath: \.restDuration), in: 1 * 60 ... 60 * 60, step: 60) {
                            Text("休息 \(Int(customPresets[index].restDuration / 60)) 分钟")
                                .font(.caption)
                        }
                    }
                }
                .padding(12)
                .background(Color.secondary.opacity(0.05), in: RoundedRectangle(cornerRadius: 14, style: .continuous))
            }
        }
    }

    private func binding(for index: Int, keyPath: WritableKeyPath<FocusPreset, TimeInterval>) -> Binding<TimeInterval> {
        Binding {
            customPresets[index][keyPath: keyPath]
        } set: { newValue in
            customPresets[index][keyPath: keyPath] = newValue
            if index + 3 == selectedPresetIndex { // currently selected custom preset
                let preset = customPresets[index]
                if service.isRunning {
                    service.stop()
                }
                service.start(with: preset)
            }
        }
    }

    private func toggleRun() {
        if !service.isRunning {
            let preset = presets[selectedPresetIndex]
            service.start(with: preset)
            return
        }
        if service.snapshot.isPaused {
            service.resume()
        } else {
            service.pause()
        }
    }

    private func formattedTime(_ value: TimeInterval) -> String {
        guard value > 0 else { return "00:00" }
        let minutes = Int(value) / 60
        let seconds = Int(value) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}
