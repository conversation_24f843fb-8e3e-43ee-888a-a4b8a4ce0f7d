import SwiftUI

struct PomodoroPageView: View {
    @ObservedObject var focusService: FocusService
    let defaultPreset: FocusPreset
    let theme: Theme
    let spec: GlassSpec
    let onSettings: () -> Void
    let onPrimary: () -> Void
    let onReset: () -> Void

    private var displayRemaining: TimeInterval {
        if focusService.snapshot.total > 0 {
            return focusService.snapshot.remaining
        } else {
            return defaultPreset.focusDuration
        }
    }

    private var isPaused: Bool { focusService.snapshot.isPaused }
    private var isRunning: Bool { focusService.isRunning }

    var body: some View {
        GeometryReader { geo in
            let isLandscape = geo.size.width > geo.size.height
            let digits = timeDigits(for: displayRemaining)
            let edgePadding: CGFloat = isLandscape ? 40 : 32
            let headerAllowance: CGFloat = isLandscape ? 92 : 120
            let controlBarHeight: CGFloat = isLandscape ? 0 : 170
            let availableHeight = max(geo.size.height - headerAllowance - controlBarHeight, isLandscape ? 220 : 200)
            let buttonSize: CGFloat = isLandscape ? 64 : 74
            let buttonSpacing: CGFloat = 28
            let controlColumnWidth: CGFloat = isLandscape ? (buttonSize + edgePadding * 1.2) : 0
            let controlColumnHeight: CGFloat = isLandscape ? (buttonSize * 3 + buttonSpacing * 2 + 20) : 0
            let contentSize = CGSize(width: geo.size.width, height: availableHeight)
            let topSpacer: CGFloat = isLandscape ? 28 : 0

            let layoutMode: LayoutMode = isLandscape ? .landscape : .portrait
            let layoutInput = LayoutInput(
                contentSize: contentSize,
                columns: 4,
                rows: isLandscape ? 1 : 2,
                edgePadding: edgePadding,
                aspectRatio: 1.32,
                minColumnSpacing: 16,
                minRowSpacing: 16,
                extraVerticalPadding: 0,
                controlColumnWidth: controlColumnWidth,
                controlColumnHeight: controlColumnHeight
            )
            let metrics = LayoutMetricsBuilder.metrics(for: layoutMode, input: layoutInput)

            VStack(spacing: 0) {
                Spacer(minLength: topSpacer)
                header
                    .padding(.top, isLandscape ? 20 : 36)
                    .padding(.horizontal, isLandscape ? 48 : 32)
                    .padding(.bottom, isLandscape ? 12 : 20)
                Spacer(minLength: isLandscape ? 12 : 0)
                if isLandscape {
                    let rowWidth = metrics.cardSize.width * 2 + metrics.digitSpacing + metrics.innerPadding * 2
                    let deckWidth = rowWidth * 2 + metrics.digitSpacing
                    let deckHeight = metrics.cardSize.height + metrics.innerPaddingVertical * 2
                    GeometryReader { inner in
                        let centerY = inner.size.height / 2
                        let deck = landscapeDeck(digits: digits, metrics: metrics)
                            .frame(width: deckWidth, alignment: .center)
                        let controlRail = verticalControlRail(buttonSize: buttonSize, spacing: buttonSpacing)
                            .frame(width: buttonSize, height: controlColumnHeight)

                        deck
                            .position(x: inner.size.width / 2, y: centerY)
                        controlRail
                            .position(x: inner.size.width - edgePadding - buttonSize / 2,
                                      y: centerY)
                    }
                    .frame(height: max(deckHeight, controlColumnHeight))
                    .padding(.horizontal, edgePadding)
                } else {
                    clockDigits(digits: digits, metrics: metrics)
                        .padding(.horizontal, 32)
                        .padding(.vertical, 4)
                    Spacer()
                    controlBar(isLandscape: false)
                        .padding(.top, 32)
                        .padding(.horizontal, 32)
                        .padding(.bottom, 36)
                }
                if isLandscape {
                    Spacer(minLength: 32)
                }
            }
            .frame(width: geo.size.width, height: geo.size.height)
        }
    }

    private var header: some View {
        VStack(spacing: 6) {
            Text("POMODORO")
                .font(.system(size: 20, weight: .semibold, design: .rounded))
                .tracking(2)
                .foregroundStyle(.secondary)
            Text(statusText)
                .font(.caption.weight(.medium))
                .foregroundStyle(.secondary)
        }
    }

    private func clockDigits(digits: (minutes: [Int], seconds: [Int]), metrics: ClockLayoutMetrics) -> some View {
        Group {
            if metrics.rowSpacing == 0 {
                HStack(spacing: metrics.digitSpacing) {
                    digitsRow(digits: digits.minutes, metrics: metrics)
                    digitsRow(digits: digits.seconds, metrics: metrics)
                }
            } else {
                VStack(spacing: metrics.rowSpacing) {
                    digitsRow(digits: digits.minutes, metrics: metrics)
                    digitsRow(digits: digits.seconds, metrics: metrics)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .center)
    }

    private func landscapeDeck(digits: (minutes: [Int], seconds: [Int]), metrics: ClockLayoutMetrics) -> some View {
        HStack(spacing: metrics.digitSpacing) {
            digitsRow(digits: digits.minutes, metrics: metrics)
            digitsRow(digits: digits.seconds, metrics: metrics)
        }
    }

    private func controlBar(isLandscape: Bool) -> some View {
        HStack(spacing: isLandscape ? 32 : 28) {
            secondaryControlButton(icon: "gearshape", action: onSettings)
            primaryControlButton(action: onPrimary)
            secondaryControlButton(icon: "arrow.counterclockwise", action: onReset)
        }
        .frame(maxWidth: .infinity)
    }

    private func verticalControlRail(buttonSize: CGFloat, spacing: CGFloat) -> some View {
        VStack(spacing: spacing) {
            railIconButton(icon: "gearshape", action: onSettings, size: buttonSize)
            railPrimaryButton(size: buttonSize)
            railIconButton(icon: "arrow.counterclockwise", action: onReset, size: buttonSize)
        }
    }

    private func railIconButton(icon: String, action: @escaping () -> Void, size: CGFloat) -> some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .frame(width: size, height: size)
                .foregroundStyle(theme.control.foreground.opacity(0.8))
        }
        .buttonStyle(.plain)
        .contentShape(Circle())
    }

    private func railPrimaryButton(size: CGFloat) -> some View {
        Button(action: onPrimary) {
            Image(systemName: primaryControlIcon)
                .font(.system(size: 28, weight: .bold))
                .frame(width: size, height: size)
                .foregroundStyle(Color.white)
                .shadow(color: theme.control.shadow.opacity(0.25), radius: 16, x: 0, y: 12)
        }
        .buttonStyle(.plain)
        .background(
            Circle()
                .fill(theme.accent)
        )
        .shadow(color: theme.accent.opacity(0.4), radius: 18, x: 0, y: 10)
    }

    private func digitsRow(digits: [Int], metrics: ClockLayoutMetrics) -> some View {
        HStack(spacing: metrics.digitSpacing) {
            ForEach(Array(digits.enumerated()), id: \.offset) { _, digit in
                FlipDigitView(
                    digit: digit,
                    theme: theme,
                    spec: spec,
                    font: metrics.font
                )
                .frame(width: metrics.cardSize.width, height: metrics.cardSize.height)
            }
        }
        .padding(.horizontal, metrics.innerPadding)
        .padding(.vertical, metrics.innerPaddingVertical)
    }

    private func primaryControlButton(action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Image(systemName: primaryControlIcon)
                .font(.system(size: 28, weight: .bold))
                .frame(width: 74, height: 74)
                .foregroundStyle(Color.white)
                .shadow(color: theme.control.shadow.opacity(0.25), radius: 16, x: 0, y: 12)
        }
        .buttonStyle(.plain)
        .background(
            Circle()
                .fill(theme.accent)
        )
        .shadow(color: theme.accent.opacity(0.4), radius: 18, x: 0, y: 10)
    }

    private func secondaryControlButton(icon: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 18, weight: .semibold))
                .frame(width: 48, height: 48)
                .foregroundStyle(theme.control.foreground.opacity(0.7))
        }
        .buttonStyle(.plain)
        .background(
            Circle()
                .fill(theme.control.base.opacity(0.35))
        )
        .overlay(
            Circle()
                .stroke(theme.control.border.opacity(0.4), lineWidth: 1)
        )
    }

    private var primaryControlIcon: String {
        if !isRunning { return "play.fill" }
        return isPaused ? "play.fill" : "pause.fill"
    }

    private var statusText: String {
        if !isRunning { return "READY" }
        if isPaused { return "PAUSED" }
        switch focusService.snapshot.phase {
        case .focus: return "FOCUS"
        case .rest: return "REST"
        }
    }

    private func timeDigits(for value: TimeInterval) -> (minutes: [Int], seconds: [Int]) {
        let clamped = max(0, Int(value))
        let minutes = clamped / 60
        let seconds = clamped % 60
        let minuteDigits = String(format: "%02d", minutes).compactMap { Int(String($0)) }
        let secondDigits = String(format: "%02d", seconds).compactMap { Int(String($0)) }
        return (minuteDigits, secondDigits)
    }
}
