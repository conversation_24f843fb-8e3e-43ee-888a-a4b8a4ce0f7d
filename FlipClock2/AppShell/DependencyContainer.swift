import Foundation

@MainActor
struct DependencyContainer {
    static let shared = DependencyContainer()

    var featureFlags: FeatureFlags = .default

    // MARK: - Factories

    func makeClockEngine() -> ClockEngine {
        DisplayLinkClockEngine()
    }

    func makeClockViewModel() -> ClockViewModel {
        ClockViewModel(engine: makeClockEngine())
    }

    func makeThemeStore() -> ThemeStore {
        ThemeStore()
    }

    func makeFocusService() -> FocusService {
        FocusService()
    }

    func makeWhiteNoiseService() -> WhiteNoiseService {
        WhiteNoiseService()
    }

    func makePersistenceStore() -> PersistenceStore {
        UserDefaultsStore()
    }

    func makeNotificationScheduler() -> NotificationScheduler {
        UNNotificationScheduler()
    }

    func makeTelemetryClient() -> TelemetryClient {
        NoopTelemetryClient()
    }
}
