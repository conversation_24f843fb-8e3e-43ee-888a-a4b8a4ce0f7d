import Combine
import SwiftUI

enum VisualMode: String, CaseIterable, Identifiable {
    case standard
    case comfort
    case solid

    var id: String { rawValue }

    var label: String {
        switch self {
        case .standard: return "标准"
        case .comfort: return "舒适"
        case .solid: return "纯色"
        }
    }
}

struct GlassSpec: Equatable {
    let blur: CGFloat
    let opacity: Double
    let highlightOpacity: Double
    let borderOpacity: Double
}

struct ControlStyle {
    let base: Color
    let highlight: Color
    let shadow: Color
    let border: Color
    let foreground: Color
    let digitHighContrast: Color
}

struct Theme: Identifiable, Equatable {
    let id: UUID
    let name: String
    let background: Color
    let digitColor: Color
    let accent: Color
    let glassSpec: GlassSpec
    let modeOverrides: [VisualMode: GlassSpec]
    let control: ControlStyle

    init(
        id: UUID = UUID(),
        name: String,
        background: Color,
        digitColor: Color,
        accent: Color,
        glassSpec: GlassSpec,
        modeOverrides: [VisualMode: GlassSpec] = [:],
        control: ControlStyle
    ) {
        self.id = id
        self.name = name
        self.background = background
        self.digitColor = digitColor
        self.accent = accent
        self.glassSpec = glassSpec
        self.modeOverrides = modeOverrides
        self.control = control
    }

    func glassSpec(for mode: VisualMode) -> GlassSpec {
        modeOverrides[mode] ?? glassSpec
    }

    static func == (lhs: Theme, rhs: Theme) -> Bool {
        lhs.id == rhs.id
    }
}

@MainActor
final class ThemeStore: ObservableObject {
    @Published private(set) var themes: [Theme]
    @Published var selectedTheme: Theme
    @Published var mode: VisualMode = .standard

    init() {
        let baseGlass = GlassSpec(blur: 24, opacity: 0.28, highlightOpacity: 0.35, borderOpacity: 0.18)
        let comfortGlass = GlassSpec(blur: 18, opacity: 0.45, highlightOpacity: 0.15, borderOpacity: 0.25)
        let solidGlass = GlassSpec(blur: 0, opacity: 0.96, highlightOpacity: 0.0, borderOpacity: 0.35)

        let defaults: [Theme] = [
            Theme(
                name: "午夜黑",
                background: Color(red: 0.02, green: 0.02, blue: 0.05),
                digitColor: Color(red: 0.92, green: 0.95, blue: 0.98),
                accent: Color(red: 0.2, green: 0.6, blue: 0.9),
                glassSpec: baseGlass,
                modeOverrides: [.comfort: comfortGlass, .solid: solidGlass],
                control: ControlStyle(
                    base: Color.white.opacity(0.12),
                    highlight: Color.white.opacity(0.35),
                    shadow: Color.black.opacity(0.6),
                    border: Color.white.opacity(0.2),
                    foreground: Color.white,
                    digitHighContrast: Color.white
                )
            ),
            Theme(
                name: "云雾白",
                background: Color(red: 0.94, green: 0.97, blue: 0.99),
                digitColor: Color(red: 0.18, green: 0.22, blue: 0.32),
                accent: Color(red: 0.34, green: 0.55, blue: 0.88),
                glassSpec: GlassSpec(blur: 26, opacity: 0.38, highlightOpacity: 0.3, borderOpacity: 0.16),
                modeOverrides: [
                    .comfort: GlassSpec(blur: 18, opacity: 0.6, highlightOpacity: 0.22, borderOpacity: 0.2),
                    .solid: GlassSpec(blur: 0, opacity: 0.92, highlightOpacity: 0.0, borderOpacity: 0.3)
                ],
                control: ControlStyle(
                    base: Color.white.opacity(0.8),
                    highlight: Color.white.opacity(0.95),
                    shadow: Color.black.opacity(0.15),
                    border: Color(red: 0.7, green: 0.78, blue: 0.9),
                    foreground: Color(red: 0.18, green: 0.22, blue: 0.32),
                    digitHighContrast: Color(red: 0.18, green: 0.22, blue: 0.32)
                )
            ),
            Theme(
                name: "霓虹蓝",
                background: Color(red: 0.04, green: 0.06, blue: 0.16),
                digitColor: Color(red: 0.82, green: 0.94, blue: 1.0),
                accent: Color(red: 0.24, green: 0.74, blue: 0.96),
                glassSpec: baseGlass,
                modeOverrides: [.comfort: comfortGlass, .solid: solidGlass],
                control: ControlStyle(
                    base: Color(red: 0.08, green: 0.12, blue: 0.24).opacity(0.65),
                    highlight: Color.cyan.opacity(0.32),
                    shadow: Color.black.opacity(0.55),
                    border: Color.cyan.opacity(0.28),
                    foreground: Color(red: 0.85, green: 0.95, blue: 1.0),
                    digitHighContrast: Color(red: 0.9, green: 0.97, blue: 1.0)
                )
            )
        ]
        _themes = Published(initialValue: defaults)
        _selectedTheme = Published(initialValue: defaults[0])
    }

    func applyTheme(_ theme: Theme) {
        selectedTheme = theme
    }

    func setMode(_ mode: VisualMode) {
        self.mode = mode
    }

    func glassSpec(for theme: Theme) -> GlassSpec {
        theme.glassSpec(for: mode)
    }
}
