import SwiftUI

struct ClockLayoutMetrics {
    let cardSize: CGSize
    let digitSpacing: CG<PERSON>loat
    let rowSpacing: CG<PERSON>loat
    let outerPadding: CGFloat
    let innerPadding: CGFloat
    let innerPaddingVertical: CGFloat
    let font: Font
    let scale: CGFloat
}

struct LayoutInput {
    let contentSize: CGSize
    let columns: Int
    let rows: Int
    let edgePadding: CGFloat
    let aspectRatio: CGFloat
    let minColumnSpacing: CGFloat
    let minRowSpacing: CGFloat
    let extraVerticalPadding: CGFloat
    let controlColumnWidth: CGFloat
    let controlColumnHeight: CGFloat
}

enum LayoutMode {
    case portrait
    case landscape
}

enum LayoutMetricsBuilder {
    static func metrics(for mode: LayoutMode, input: LayoutInput) -> ClockLayoutMetrics {
        switch mode {
        case .portrait:
            return portraitMetrics(input: input)
        case .landscape:
            return landscapeMetrics(input: input)
        }
    }

    private static func portraitMetrics(input: LayoutInput) -> ClockLayoutMetrics {
        let columnsPerRow = 2
        var cardWidth = (input.contentSize.width - 2 * input.edgePadding - CGFloat(columnsPerRow - 1) * input.minColumnSpacing) / CGFloat(columnsPerRow)
        let minCardWidth: CGFloat = 72
        cardWidth = max(cardWidth, minCardWidth)

        var cardHeight = cardWidth * input.aspectRatio
    
        var digitSpacing = max(cardWidth * 0.08, input.minColumnSpacing)
        var innerPadding = max(cardWidth * 0.04, 8)
        var innerPaddingVertical = max(cardHeight * 0.05, 8)
        var rowSpacing = max(cardWidth * 0.18, input.minRowSpacing)

        let requiredHeight = CGFloat(input.rows) * cardHeight + CGFloat(max(0, input.rows - 1)) * rowSpacing + innerPaddingVertical * 2
        if requiredHeight > input.contentSize.height {
            let scale = input.contentSize.height / requiredHeight
            cardWidth *= scale
            cardHeight *= scale
            digitSpacing *= scale
            innerPadding *= scale
            innerPaddingVertical *= scale
            rowSpacing *= scale
        }

        let totalRowWidth = CGFloat(columnsPerRow) * cardWidth + CGFloat(columnsPerRow - 1) * digitSpacing + innerPadding * 2
        var outerPadding = max((input.contentSize.width - totalRowWidth) / 2, input.edgePadding)
        if totalRowWidth > input.contentSize.width {
            let widthScale = (input.contentSize.width - 2 * input.edgePadding) / (totalRowWidth)
            cardWidth *= widthScale
            cardHeight *= widthScale
            digitSpacing *= widthScale
            innerPadding *= widthScale
            innerPaddingVertical *= widthScale
            rowSpacing *= widthScale
            outerPadding = input.edgePadding
        }

        let fontSize = cardWidth * 0.78

        return ClockLayoutMetrics(
            cardSize: CGSize(width: cardWidth, height: cardHeight),
            digitSpacing: digitSpacing,
            rowSpacing: rowSpacing,
            outerPadding: outerPadding,
            innerPadding: innerPadding,
            innerPaddingVertical: innerPaddingVertical,
            font: .system(size: fontSize, weight: .semibold, design: .rounded).monospacedDigit(),
            scale: 1
        )
    }

    private static func landscapeMetrics(input: LayoutInput) -> ClockLayoutMetrics {
        var availableWidth = input.contentSize.width - input.controlColumnWidth
        var cardWidth = (availableWidth - 2 * input.edgePadding - CGFloat(input.columns - 1) * input.minColumnSpacing) / CGFloat(input.columns)
        let maxWidth = input.contentSize.width * 0.18
        let minWidth: CGFloat = 72
        cardWidth = min(max(cardWidth, minWidth), maxWidth)

        var cardHeight = cardWidth * input.aspectRatio
        let maxHeightCandidate = max(input.contentSize.height - input.extraVerticalPadding, 120)
        let maxHeight = max(maxHeightCandidate, input.controlColumnHeight)
        if cardHeight > maxHeight {
            let scale = maxHeight / cardHeight
            cardWidth *= scale
            cardHeight = maxHeight
        }

        var digitSpacing = max(cardWidth * 0.08, input.minColumnSpacing)
        var innerPadding = max(cardWidth * 0.04, 8)
        let totalWidth = CGFloat(input.columns) * cardWidth + CGFloat(max(0, input.columns - 1)) * digitSpacing + innerPadding * 2
        var outerPadding = max((input.contentSize.width - totalWidth) / 2, input.edgePadding)
        if totalWidth > input.contentSize.width {
            let scale = (input.contentSize.width - 2 * input.edgePadding) / totalWidth
            cardWidth *= scale
            cardHeight *= scale
            digitSpacing *= scale
            innerPadding *= scale
            outerPadding = input.edgePadding
        }

        let fontSize = cardWidth * 0.8

        return ClockLayoutMetrics(
            cardSize: CGSize(width: cardWidth, height: cardHeight),
            digitSpacing: digitSpacing,
            rowSpacing: 0,
            outerPadding: outerPadding,
            innerPadding: innerPadding,
            innerPaddingVertical: 0,
            font: .system(size: fontSize, weight: .semibold, design: .rounded).monospacedDigit(),
            scale: 1
        )
    }
}
