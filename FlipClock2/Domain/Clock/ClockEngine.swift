import Foundation
import Combine
import QuartzCore

struct ClockSnapshot: Equatable {
    let date: Date
    let components: DateComponents
    let shouldFlip: Bool

    static let placeholder = ClockSnapshot(
        date: .now,
        components: Calendar.autoupdatingCurrent.dateComponents([.hour, .minute, .second], from: .now),
        shouldFlip: false
    )
}

protocol ClockEngine: AnyObject {
    var tickStream: AsyncStream<ClockSnapshot> { get }
    func start()
    func stop()
}

final class DisplayLinkClockEngine: NSObject, ClockEngine {
    private let calendar: Calendar
    private var displayLink: CADisplayLink?
    private var lastComponents: DateComponents?
    private let stream: AsyncStream<ClockSnapshot>
    private var continuation: AsyncStream<ClockSnapshot>.Continuation?
    private var isRunning = false

    override init() {
        self.calendar = .autoupdatingCurrent
        var continuationStorage: AsyncStream<ClockSnapshot>.Continuation?
        self.stream = AsyncStream { continuation in
            continuationStorage = continuation
        }
        super.init()
        self.continuation = continuationStorage
    }

    var tickStream: AsyncStream<ClockSnapshot> { stream }

    func start() {
        guard !isRunning else { return }
        configureDisplayLink()
        isRunning = true
    }

    func stop() {
        displayLink?.invalidate()
        displayLink = nil
        isRunning = false
    }

    deinit {
        stop()
        continuation?.finish()
    }

    private func configureDisplayLink() {
        displayLink = CADisplayLink(target: self, selector: #selector(step))
        displayLink?.preferredFramesPerSecond = 60
        displayLink?.add(to: .main, forMode: .common)
    }

    @objc private func step() {
        let now = Date()
        let components = calendar.dateComponents([.hour, .minute, .second], from: now)
        let shouldFlip = shouldFlip(from: components)
        let snapshot = ClockSnapshot(date: now, components: components, shouldFlip: shouldFlip)
        continuation?.yield(snapshot)
    }

    private func shouldFlip(from components: DateComponents) -> Bool {
        defer { lastComponents = components }
        guard let lastComponents else { return true }
        return components != lastComponents
    }
}
