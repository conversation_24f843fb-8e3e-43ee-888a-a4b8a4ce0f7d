import Foundation
import Combine

enum FocusPhase: Equatable {
    case focus
    case rest

    var displayName: String {
        switch self {
        case .focus: return "专注"
        case .rest: return "休息"
        }
    }
}

struct FocusPreset: Identifiable, Equatable {
    let id: UUID = UUID()
    var name: String
    var focusDuration: TimeInterval
    var restDuration: TimeInterval

    var description: String {
        "\(formatted(minutes: focusDuration)) / \(formatted(minutes: restDuration))"
    }

    private func formatted(minutes value: TimeInterval) -> String {
        let minutes = Int(value / 60)
        return "\(minutes)min"
    }
}

struct FocusTickerSnapshot: Equatable {
    var phase: FocusPhase
    var remaining: TimeInterval
    var total: TimeInterval
    var cycle: Int
    var isPaused: Bool

    static let idle = FocusTickerSnapshot(phase: .focus, remaining: 0, total: 0, cycle: 0, isPaused: false)
}

@MainActor
final class FocusService: ObservableObject {
    @Published private(set) var snapshot: FocusTickerSnapshot = .idle
    @Published private(set) var isRunning: Bool = false

    private var configuration: FocusPreset?
    private var tickerTask: Task<Void, Never>?
    private var lastTick: Date?

    func start(with preset: FocusPreset) {
        configuration = preset
        snapshot = FocusTickerSnapshot(phase: .focus,
                                       remaining: preset.focusDuration,
                                       total: preset.focusDuration,
                                       cycle: 1,
                                       isPaused: false)
        isRunning = true
        startTicker()
    }

    func stop() {
        tickerTask?.cancel()
        tickerTask = nil
        configuration = nil
        lastTick = nil
        snapshot = .idle
        isRunning = false
    }

    func pause() {
        guard isRunning, tickerTask != nil else { return }
        tickerTask?.cancel()
        tickerTask = nil
        lastTick = nil
        snapshot.isPaused = true
    }

    func resume() {
        guard configuration != nil, isRunning else { return }
        guard !snapshot.isPaused else {
            snapshot.isPaused = false
            startTicker()
            return
        }
    }

    func skipPhase() {
        guard configuration != nil else { return }
        transitionToNextPhase()
    }

    // MARK: - Private helpers

    private func startTicker() {
        tickerTask?.cancel()
        lastTick = Date()
        tickerTask = Task { [weak self] in
            guard let self else { return }
            let clock = ContinuousClock()
            while !Task.isCancelled {
                let now = Date()
                if let lastTick = self.lastTick {
                    let delta = now.timeIntervalSince(lastTick)
                    await self.advance(by: delta)
                }
                self.lastTick = now
                do {
                    try await clock.sleep(for: .milliseconds(200))
                } catch {
                    break
                }
            }
        }
    }

    private func advance(by delta: TimeInterval) async {
        guard isRunning, !snapshot.isPaused, snapshot.remaining > 0 else { return }
        snapshot.remaining = max(0, snapshot.remaining - delta)
        if snapshot.remaining == 0 {
            transitionToNextPhase()
        }
    }

    private func transitionToNextPhase() {
        guard let configuration else { stop(); return }
        switch snapshot.phase {
        case .focus:
            snapshot.phase = .rest
            snapshot.remaining = configuration.restDuration
            snapshot.total = configuration.restDuration
            snapshot.isPaused = false
            snapshot.cycle = max(snapshot.cycle, 1)
        case .rest:
            snapshot.phase = .focus
            snapshot.remaining = configuration.focusDuration
            snapshot.total = configuration.focusDuration
            snapshot.isPaused = false
            snapshot.cycle += 1
        }
        lastTick = Date()
        startTicker()
    }
}
