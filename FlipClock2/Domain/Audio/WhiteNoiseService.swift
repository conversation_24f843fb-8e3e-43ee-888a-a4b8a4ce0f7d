import Foundation
import Combine
import AVFoundation
import SwiftUI

enum WhiteNoiseProfile: String, CaseIterable, Identifiable {
    case rainfall
    case cafe
    case brown

    var id: String { rawValue }

    var displayName: String {
        switch self {
        case .rainfall: return "雨滴"
        case .cafe: return "咖啡馆"
        case .brown: return "棕噪"
        }
    }
}

@MainActor
final class WhiteNoiseService: ObservableObject {
    @Published private(set) var isPlaying: Bool = false
    @Published private(set) var currentProfile: WhiteNoiseProfile?
    @Published var volume: Float = 0.6 {
        didSet { player.volume = volume }
    }

    private let engine = AVAudioEngine()
    private let player = AVAudioPlayerNode()
    private var buffers: [WhiteNoiseProfile: AVAudioPCMBuffer] = [:]
    private var sessionConfigured = false

    init() {
        engine.attach(player)
        let mainMixer = engine.mainMixerNode
        engine.connect(player, to: mainMixer, format: makeFormat())
    }

    func play(profile: WhiteNoiseProfile) {
        configureSessionIfNeeded()
        prepareEngineIfNeeded()

        stopScheduling()

        let buffer = buffer(for: profile)
        player.scheduleBuffer(buffer, at: nil, options: [.loops])
        player.volume = volume
        if !player.isPlaying {
            player.play()
        }
        isPlaying = true
        currentProfile = profile
    }

    func stop() {
        player.stop()
        isPlaying = false
        currentProfile = nil
    }

    private func configureSessionIfNeeded() {
        guard !sessionConfigured else { return }
        #if os(iOS)
        do {
            let session = AVAudioSession.sharedInstance()
            try session.setCategory(.ambient, mode: .default, options: [.mixWithOthers])
            try session.setActive(true, options: [])
            sessionConfigured = true
        } catch {
            print("[WhiteNoiseService] Failed to configure session: \(error)")
        }
        #endif
    }

    private func prepareEngineIfNeeded() {
        if !engine.isRunning {
            do {
                try engine.start()
            } catch {
                print("[WhiteNoiseService] Engine start failed: \(error)")
            }
        }
    }

    private func stopScheduling() {
        player.stop()
    }

    private func buffer(for profile: WhiteNoiseProfile) -> AVAudioPCMBuffer {
        if let cached = buffers[profile] { return cached }
        let format = makeFormat()
        let length: AVAudioFrameCount = 44100
        guard let buffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: length) else {
            fatalError("Unable to allocate buffer")
        }
        buffer.frameLength = length

        let channelCount = Int(format.channelCount)
        let pointer = buffer.floatChannelData!

        for channel in 0..<channelCount {
            let samples = pointer[channel]
            switch profile {
            case .rainfall:
                fillRainfall(into: samples, frames: Int(length), sampleRate: Int(format.sampleRate))
            case .cafe:
                fillCafe(into: samples, frames: Int(length))
            case .brown:
                fillBrown(into: samples, frames: Int(length))
            }
        }

        buffers[profile] = buffer
        return buffer
    }

    private func makeFormat() -> AVAudioFormat {
        AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!
    }

    private func fillRainfall(into samples: UnsafeMutablePointer<Float>, frames: Int, sampleRate: Int) {
        var last: Float = 0
        for i in 0..<frames {
            let white = Float.random(in: -1...1)
            last = (last * 0.8) + (white * 0.2)
            let modulation = sinf(Float(i) / Float(sampleRate) * 12) * 0.2 + 0.8
            samples[i] = last * 0.35 * modulation
        }
    }

    private func fillCafe(into samples: UnsafeMutablePointer<Float>, frames: Int) {
        for i in 0..<frames {
            let base = Float.random(in: -0.5...0.5)
            let chatter = Float.random(in: -1...1) * 0.05
            samples[i] = (base * 0.4) + chatter
        }
    }

    private func fillBrown(into samples: UnsafeMutablePointer<Float>, frames: Int) {
        var brown: Float = 0
        for i in 0..<frames {
            let white = Float.random(in: -1...1)
            brown += white * 0.02
            brown *= 0.998
            samples[i] = brown.clamped(to: -0.8...0.8)
        }
    }
}

private extension Float {
    func clamped(to range: ClosedRange<Float>) -> Float {
        min(max(self, range.lowerBound), range.upperBound)
    }
}
