import Foundation
import UserNotifications

protocol NotificationScheduler {
    func requestAuthorizationIfNeeded() async
    func scheduleLocalNotification(identifier: String, content: UNNotificationContent, trigger: UNNotificationTrigger)
    func cancelNotification(identifier: String)
}

final class UNNotificationScheduler: NotificationScheduler {
    private let center: UNUserNotificationCenter

    init(center: UNUserNotificationCenter = .current()) {
        self.center = center
    }

    func requestAuthorizationIfNeeded() async {
        let options: UNAuthorizationOptions = [.alert, .sound, .badge]
        do {
            let granted = try await center.requestAuthorization(options: options)
            if !granted {
                print("[NotificationScheduler] Authorization not granted")
            }
        } catch {
            print("[NotificationScheduler] Authorization error: \(error)")
        }
    }

    func scheduleLocalNotification(identifier: String, content: UNNotificationContent, trigger: UNNotificationTrigger) {
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        center.add(request) { error in
            if let error {
                print("[NotificationScheduler] schedule error: \(error)")
            }
        }
    }

    func cancelNotification(identifier: String) {
        center.removePendingNotificationRequests(withIdentifiers: [identifier])
    }
}
