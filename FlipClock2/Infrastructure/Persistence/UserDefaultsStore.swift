import Foundation

protocol PersistenceStore {
    func set<T: Codable>(_ value: T, forKey key: String)
    func value<T: Codable>(forKey key: String, default defaultValue: @autoclosure () -> T) -> T
}

struct UserDefaultsStore: PersistenceStore {
    private let defaults: UserDefaults

    init(defaults: UserDefaults = .standard) {
        self.defaults = defaults
    }

    func set<T: Codable>(_ value: T, forKey key: String) {
        guard let data = try? JSONEncoder().encode(value) else { return }
        defaults.set(data, forKey: key)
    }

    func value<T: Codable>(forKey key: String, default defaultValue: @autoclosure () -> T) -> T {
        guard let data = defaults.data(forKey: key),
              let decoded = try? JSONDecoder().decode(T.self, from: data) else {
            return defaultValue()
        }
        return decoded
    }
}
