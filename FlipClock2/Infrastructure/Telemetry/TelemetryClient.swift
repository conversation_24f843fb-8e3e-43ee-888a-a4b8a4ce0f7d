import Foundation

protocol TelemetryClient {
    func track(event: TelemetryEvent)
}

struct TelemetryEvent: Codable {
    let name: String
    let metadata: [String: String]
    let timestamp: Date

    init(name: String, metadata: [String: String] = [:], timestamp: Date = .now) {
        self.name = name
        self.metadata = metadata
        self.timestamp = timestamp
    }
}

struct NoopTelemetryClient: TelemetryClient {
    func track(event: TelemetryEvent) {
        #if DEBUG
        print("[Telemetry] \(event.name) metadata=\(event.metadata)")
        #endif
    }
}
