import Foundation
import os

enum AppLogger {
    private static let subsystem = Bundle.main.bundleIdentifier ?? "com.flipclock.app"

    static let clock = Logger(subsystem: subsystem, category: "clock")
    static let focus = Logger(subsystem: subsystem, category: "focus")
    static let audio = Logger(subsystem: subsystem, category: "audio")
    static let infrastructure = Logger(subsystem: subsystem, category: "infra")
}
