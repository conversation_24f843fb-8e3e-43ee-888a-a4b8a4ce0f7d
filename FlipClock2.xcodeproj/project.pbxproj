// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		E0BEA0402E82BEF300E80D2A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E0BEA02A2E82BEED00E80D2A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E0BEA0312E82BEED00E80D2A;
			remoteInfo = FlipClock2;
		};
		E0BEA04A2E82BEF400E80D2A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E0BEA02A2E82BEED00E80D2A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E0BEA0312E82BEED00E80D2A;
			remoteInfo = FlipClock2;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		E0BEA0322E82BEED00E80D2A /* FlipClock2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FlipClock2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E0BEA03F2E82BEF300E80D2A /* FlipClock2Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FlipClock2Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E0BEA0492E82BEF400E80D2A /* FlipClock2UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FlipClock2UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		E0BEA0342E82BEED00E80D2A /* FlipClock2 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FlipClock2;
			sourceTree = "<group>";
		};
		E0BEA0422E82BEF300E80D2A /* FlipClock2Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FlipClock2Tests;
			sourceTree = "<group>";
		};
		E0BEA04C2E82BEF400E80D2A /* FlipClock2UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FlipClock2UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		E0BEA02F2E82BEED00E80D2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E0BEA03C2E82BEF300E80D2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E0BEA0462E82BEF400E80D2A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		E0BEA0292E82BEED00E80D2A = {
			isa = PBXGroup;
			children = (
				E0BEA0342E82BEED00E80D2A /* FlipClock2 */,
				E0BEA0422E82BEF300E80D2A /* FlipClock2Tests */,
				E0BEA04C2E82BEF400E80D2A /* FlipClock2UITests */,
				E0BEA0332E82BEED00E80D2A /* Products */,
			);
			sourceTree = "<group>";
		};
		E0BEA0332E82BEED00E80D2A /* Products */ = {
			isa = PBXGroup;
			children = (
				E0BEA0322E82BEED00E80D2A /* FlipClock2.app */,
				E0BEA03F2E82BEF300E80D2A /* FlipClock2Tests.xctest */,
				E0BEA0492E82BEF400E80D2A /* FlipClock2UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E0BEA0312E82BEED00E80D2A /* FlipClock2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E0BEA0532E82BEF400E80D2A /* Build configuration list for PBXNativeTarget "FlipClock2" */;
			buildPhases = (
				E0BEA02E2E82BEED00E80D2A /* Sources */,
				E0BEA02F2E82BEED00E80D2A /* Frameworks */,
				E0BEA0302E82BEED00E80D2A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				E0BEA0342E82BEED00E80D2A /* FlipClock2 */,
			);
			name = FlipClock2;
			packageProductDependencies = (
			);
			productName = FlipClock2;
			productReference = E0BEA0322E82BEED00E80D2A /* FlipClock2.app */;
			productType = "com.apple.product-type.application";
		};
		E0BEA03E2E82BEF300E80D2A /* FlipClock2Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E0BEA0562E82BEF400E80D2A /* Build configuration list for PBXNativeTarget "FlipClock2Tests" */;
			buildPhases = (
				E0BEA03B2E82BEF300E80D2A /* Sources */,
				E0BEA03C2E82BEF300E80D2A /* Frameworks */,
				E0BEA03D2E82BEF300E80D2A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E0BEA0412E82BEF300E80D2A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E0BEA0422E82BEF300E80D2A /* FlipClock2Tests */,
			);
			name = FlipClock2Tests;
			packageProductDependencies = (
			);
			productName = FlipClock2Tests;
			productReference = E0BEA03F2E82BEF300E80D2A /* FlipClock2Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		E0BEA0482E82BEF400E80D2A /* FlipClock2UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E0BEA0592E82BEF400E80D2A /* Build configuration list for PBXNativeTarget "FlipClock2UITests" */;
			buildPhases = (
				E0BEA0452E82BEF400E80D2A /* Sources */,
				E0BEA0462E82BEF400E80D2A /* Frameworks */,
				E0BEA0472E82BEF400E80D2A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E0BEA04B2E82BEF400E80D2A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E0BEA04C2E82BEF400E80D2A /* FlipClock2UITests */,
			);
			name = FlipClock2UITests;
			packageProductDependencies = (
			);
			productName = FlipClock2UITests;
			productReference = E0BEA0492E82BEF400E80D2A /* FlipClock2UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E0BEA02A2E82BEED00E80D2A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					E0BEA0312E82BEED00E80D2A = {
						CreatedOnToolsVersion = 26.0.1;
					};
					E0BEA03E2E82BEF300E80D2A = {
						CreatedOnToolsVersion = 26.0.1;
						TestTargetID = E0BEA0312E82BEED00E80D2A;
					};
					E0BEA0482E82BEF400E80D2A = {
						CreatedOnToolsVersion = 26.0.1;
						TestTargetID = E0BEA0312E82BEED00E80D2A;
					};
				};
			};
			buildConfigurationList = E0BEA02D2E82BEED00E80D2A /* Build configuration list for PBXProject "FlipClock2" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E0BEA0292E82BEED00E80D2A;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = E0BEA0332E82BEED00E80D2A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E0BEA0312E82BEED00E80D2A /* FlipClock2 */,
				E0BEA03E2E82BEF300E80D2A /* FlipClock2Tests */,
				E0BEA0482E82BEF400E80D2A /* FlipClock2UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E0BEA0302E82BEED00E80D2A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E0BEA03D2E82BEF300E80D2A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E0BEA0472E82BEF400E80D2A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E0BEA02E2E82BEED00E80D2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E0BEA03B2E82BEF300E80D2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E0BEA0452E82BEF400E80D2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		E0BEA0412E82BEF300E80D2A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E0BEA0312E82BEED00E80D2A /* FlipClock2 */;
			targetProxy = E0BEA0402E82BEF300E80D2A /* PBXContainerItemProxy */;
		};
		E0BEA04B2E82BEF400E80D2A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E0BEA0312E82BEED00E80D2A /* FlipClock2 */;
			targetProxy = E0BEA04A2E82BEF400E80D2A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		E0BEA0512E82BEF400E80D2A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = JA49UXHZ57;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E0BEA0522E82BEF400E80D2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = JA49UXHZ57;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E0BEA0542E82BEF400E80D2A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JA49UXHZ57;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wdstudio.FlipClock2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E0BEA0552E82BEF400E80D2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JA49UXHZ57;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wdstudio.FlipClock2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		E0BEA0572E82BEF400E80D2A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JA49UXHZ57;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wdstudio.FlipClock2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FlipClock2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FlipClock2";
			};
			name = Debug;
		};
		E0BEA0582E82BEF400E80D2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JA49UXHZ57;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wdstudio.FlipClock2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FlipClock2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FlipClock2";
			};
			name = Release;
		};
		E0BEA05A2E82BEF400E80D2A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JA49UXHZ57;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wdstudio.FlipClock2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FlipClock2;
			};
			name = Debug;
		};
		E0BEA05B2E82BEF400E80D2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JA49UXHZ57;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wdstudio.FlipClock2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FlipClock2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E0BEA02D2E82BEED00E80D2A /* Build configuration list for PBXProject "FlipClock2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E0BEA0512E82BEF400E80D2A /* Debug */,
				E0BEA0522E82BEF400E80D2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E0BEA0532E82BEF400E80D2A /* Build configuration list for PBXNativeTarget "FlipClock2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E0BEA0542E82BEF400E80D2A /* Debug */,
				E0BEA0552E82BEF400E80D2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E0BEA0562E82BEF400E80D2A /* Build configuration list for PBXNativeTarget "FlipClock2Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E0BEA0572E82BEF400E80D2A /* Debug */,
				E0BEA0582E82BEF400E80D2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E0BEA0592E82BEF400E80D2A /* Build configuration list for PBXNativeTarget "FlipClock2UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E0BEA05A2E82BEF400E80D2A /* Debug */,
				E0BEA05B2E82BEF400E80D2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E0BEA02A2E82BEED00E80D2A /* Project object */;
}
