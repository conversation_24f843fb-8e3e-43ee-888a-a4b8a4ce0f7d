# 概览
目标：在 iOS 26 的 **Liquid Glass** 设计语言下，打造一款“极简美学 × 高效专注”的翻页时钟 App，采用 **订阅 + 轻广告** 的混合商业模式，8 周上线 MVP，并在随后 3 个小版本中拉升留存与付费。

---

# 设计遵循（面向 iOS 26 Liquid Glass 的落地要点）
> 本节将 Liquid Glass 的官方设计取向转化为我们可操作的产品与视觉规范。

**设计原则**
1) **内容优先**：控件以“浮层玻璃”呈现，自动为内容让位；重要信息（时间数字、计时进度）**永远不被折射/模糊**。
2) **动态材质**：使用半透明、具折射与高光的“玻璃”材质表现控件与导航；根据浅/深色与壁纸环境自适应。
3) **层级与深度**：小型控件（按钮/开关/滑块）与大型结构（标签栏/侧边栏）都采用层级分组，保障感知与可发现性。
4) **动效克制**：动效体现“流动性”和“弹性”，但尊重“降低动态效果/降低透明度/提高对比度”等无障碍设置。
5) **平台一致性**：Tab 栏随滚动收放；Sidebar 可折射背景；锁屏/主屏/StandBy 的时间与组件使用一致的材质语言。

**对我们 App 的关键约束**
- **时间数字（时钟主体）使用纯净、非折射层**，保证清晰可读；玻璃材质仅用于悬浮控件与工具栏。
- 提供三档视觉模式：
  - *标准*（默认）：玻璃控件 + 轻高光；
  - *舒适*：提高对比、降低透明，弱化高光；
  - *纯色*：控件改为不透明卡片，去除折射与高光（配合“降低透明度”无障碍首选项）。
- 文本排版：SF 字体（Large/XXL 数字），数字字距与像素对齐优化；深浅色状态下保底对比度 ≥ 7:1。
- 动效：时钟翻页 60fps；控件玻璃高光与反射采用轻量级图层叠加；Respect 120Hz，但设置帧率上限与降帧策略。

**组件与模式**
- **Clock 主屏**：全屏数字；右上角“玻璃浮层”放置设置/主题入口；底部短滑呼出番茄/白噪音快速面板（玻璃抽屉）。
- **番茄钟**：玻璃“药丸”进度条（支持灵动岛/Live Activities 的一致视觉）；完成/休息切换带丝滑玻璃光晕过渡。
- **主题库**：大卡片网格，卡片为玻璃材质，悬停/选中出现高光；支持“试用”徽标与付费蒙层（不遮挡缩略图）。
- **设置**：分组玻璃面板，分节标题采用磨砂/高光混合材质；“视觉模式”三档切换放在首屏。
- **StandBy & Widget**：数字采用纯净层；Widget 边框使用极轻玻璃描边，避免信息被折射影响可读性。

---

# 视觉与交互规范（V1 草案）
**色彩**
- 基础：系统动态中性色（Label/Secondary/Quaternary），避免自造异常对比。
- 强调色：两套可切换主题色 *Aqua* / *Amber*（仅用于控件高光与选中描边）。

**材质（建议）**
- 玻璃层：半透明 + 轻折射 + 柔性高光（暗色下降低透明度、提高边界对比）。
- 阴影与高光：双层投影（环境阴影 + 近场阴影）、高光采用径向渐变细窄高亮带。

**动效时间（基线）**
- 控件显隐 180–220ms；抽屉 260ms；Tab 收放 180ms；翻页帧保持 60fps，过载时优先保证数字清晰与帧稳定。

**无障碍**
- 支持“提高对比度/降低透明度/减少动态效果”，即时重绘材质与动效幅度。
- 夜间护眼：自动降亮 + 像素级微位移防烧屏（每 60–90s 移动 ≤ 2pt）。

---

# 任务看板（8 周路线图，含验收标准）
> 可直接导入 Jira/Linear；假设团队：iOS ×2、设计 ×1、增长/PM ×1（兼职）、QA ×0.5

## EPIC A｜核心时钟与动效
- A1 时钟引擎：系统时间对齐，整点校正；漂移 < 5ms；✅ **验收**：连续 12h 误差 < 0.1s。
- A2 翻页动画：上/下半页 3D 翻转 + 阴影；掉帧率 < 1%；✅ 场景：低电量模式/旋转/多语言。
- A3 数字排版：SF 数字宽度/字距自适；像素对齐；✅ 6–8 语言与 12/24h/日期切换全通过。

## EPIC B｜Liquid Glass 视觉落地
- B1 玻璃组件库：按钮/开关/滑块/卡片/抽屉；✅ 三档视觉模式切换 100% 覆盖。
- B2 高光与折射：在 A14–A18 机型保持 55+ fps；✅ GPU 峰值 < 60%。
- B3 主题 1.0：黑白/复古/霓虹/纸感/奶油 5–6 套；✅ 暗色对比度 ≥ 7:1。

## EPIC C｜番茄钟与白噪音
- C1 番茄基础：25/5、自定义 2 套预设；本地通知；✅ 复归场景无漏。
- C2 白噪音：雨/咖啡馆/棕噪 3 种，循环无断点；后台播放开关；✅ 音频功耗测试通过。
- C3 Live Activities/灵动岛：进度与操作一致；✅ 锁屏/灵动岛延迟 < 300ms。

## EPIC D｜生态与系统集成
- D1 Widget：小/中/大；锁屏时钟组件；✅ 适配深浅色。
- D2 StandBy：横置特化大数字 + 低亮；✅ OLED 防烧屏策略生效。

## EPIC E｜商业化
- E1 订阅/买断：$0.99/月、$7.99/年、$14.99 终身（早鸟）；7 天试用；✅ 购买/退款/恢复完备。
- E2 付费墙与分层：免费含轻广告（设置/主题页），Pro 去广告 + 高级功能；✅ 付费漏斗埋点齐全。
- E3 广告位：仅设置/主题页原生位，频控 ≤ 3/日；✅ 不在时钟/专注中出现。

## EPIC F｜质量与性能
- F1 崩溃率 < 0.3%；启动 < 1.5s；内存 < 150MB；✅ 旧机型回归通过。
- F2 无障碍回归：提高对比/降低透明/减少动态；✅ 即时切换不重启。

## EPIC G｜交付与增长
- G1 ASO 物料：图标/截图/预览视频；英文/简中文案；✅ 通过 App 审核。
- G2 TestFlight：外测 100–200 人；两组价格/试用 A/B；✅ 报告与决策。
- G3 上线联动：达人 10–20 位素材包与兑换码；✅ 首周复盘。

> 每个任务默认字段：Owner、优先级（P0/P1）、预估（Story Points/人日）、依赖关系、验收标准、回滚策略。

---

# PRD（MVP V1.0）

## 1. 背景与目标
- 以 Liquid Glass 视觉语言打造一款“摆件级”美学 + 专注效率的翻页时钟，服务愿意付费的人群。
- 8 周验证：留存（D1 ≥ 35%、D7 ≥ 15%）、订阅转化（30 天 ≥ 2.5%）、评分 ≥ 4.6。

## 2. 用户与场景
- 学生/自习：专注计时、仪式感桌面；
- 上班族/远程：桌面 StandBy、整点提醒；
- 极简/数码美学爱好者：装饰性时钟 + 主题定制。

## 3. 范围（In / Out）
**In**：时钟/番茄/白噪音、Widget/StandBy/Live Activities、主题 5–6 套、订阅/买断、轻广告、三档视觉模式。
**Out**：日历/待办/天气集成、主题编辑器、Apple Watch、Mac 版（置于 v1.2+）。

## 4. 需求明细（含验收）
### 4.1 Clock 主屏
- R-CLK-01：全屏数字（12/24h、日期/秒可切）。**验收**：切换不跳帧、像素对齐。
- R-CLK-02：右上玻璃浮层（设置/主题）。**验收**：长按出现细节卡。
- R-CLK-03：下滑抽屉（番茄/白噪音）。**验收**：220–260ms 展开收起流畅。
- R-CLK-04：三档视觉模式切换即时生效。**验收**：文字/边界对比符合 WCAG。

### 4.2 番茄钟
- R-PMD-01：25/5，支持两个自定义预设。**验收**：切换时保留进度策略明确。
- R-PMD-02：完成提醒（声音/触感/通知）。**验收**：前台/锁屏/静音场景全覆盖。
- R-PMD-03：Live Activities/灵动岛同步进度。**验收**：锁屏点按可暂停/继续。

### 4.3 白噪音
- R-SND-01：3 种音色，循环无缝。**验收**：频谱无突变、CPU/功耗合格。
- R-SND-02：后台播放开关。**验收**：切后台 2h 不异常耗电。

### 4.4 主题
- R-THM-01：5–6 套主题（深浅色）。**验收**：不同壁纸下不影响可读性。
- R-THM-02：Pro 专享主题标识/蒙层。**验收**：可试用预览 30 秒。

### 4.5 Widget/StandBy/无障碍
- R-WDG-01：主屏/锁屏 Widget。**验收**：深浅兼容、时区切换正确。
- R-SB-01：StandBy 横置大数字 + 低亮。**验收**：烧屏防护策略生效。
- R-A11Y-01：提高对比/降低透明/减少动态。**验收**：即时切换、动效幅度降级。

### 4.6 商业化
- R-BIZ-01：订阅/买断/试用。**验收**：恢复购买/家庭共享（如适用）。
- R-BIZ-02：付费墙触发（第 3 次打开/第 3 个番茄/高级主题）。**验收**：事件埋点完整。
- R-ADV-01：轻广告（设置/主题页），频控 ≤ 3/日。**验收**：核心场景零广告。

## 5. 分析与埋点（仅匿名）
- 关键事件：AppOpen、ApplyTheme、StartPomodoro、CompletePomodoro、OpenPaywall、StartTrial、Purchase、CancelTrial、AdImpression、WidgetAdd。
- 转化漏斗：Paywall View → Trial Start → Trial Convert → M1 Retention。

## 6. 性能与质量指标
- 冷启动 < 1.5s；时钟页 60fps；掉帧率 < 1%；崩溃率 < 0.3%；内存 < 150MB。

## 7. 合规与隐私
- 不采集个人身份信息；设置页提供开关与指引；广告不在核心场景展示；
- 版权：字体/音色/插画与图标来源合法可溯源。

## 8. 上线与推广
- ASO：关键词集与截图脚本；
- 社媒：桌面摆拍模板（含 StandBy）；
- 达人首发：素材包（视频片段/截图/兑换码）。

## 9. 风险与对策
- 视觉不适风险：提供“舒适/纯色”模式，默认根据无障碍自动切换；
- 帧率/耗电：降帧/降亮策略；低端机自动弱化高光；
- 审核：不使用后台保活；白噪音后台需用户同意并明确说明。

---

# 资产清单（MVP）
- 主题 PSD/Sketch/Figma 源文件；
- 图标（App Icon + 营销图标 + 主题缩略图）；
- 白噪音 3 轨（44.1kHz/立体声/无缝循环）；
- App Store 素材：标题/副标题/关键词/截图 8 张/预览视频脚本与分镜。

---

# 测试计划（提纲）
- 功能：各需求用例 + 边界场景；
- 兼容：iOS 26 最新与下探 iOS 16；
- 性能：帧率/功耗/内存/冷启动；
- 无障碍：三项系统开关联动；
- 回归：订阅/试用/退款/恢复；广告频控与位置校验。

---

# 版本路线（摘）
- **v1.1（4–6 周）**：高级番茄统计、更多主题月更、自动情景（夜间/低电量）；
- **v1.2（6–8 周）**：日历/提醒集成、轻量主题编辑器（Pro）、iCloud 同步；
- **v1.3（8–10 周）**：Apple Watch 简版、Mac Catalyst 桌面版、固定“上新日”。

