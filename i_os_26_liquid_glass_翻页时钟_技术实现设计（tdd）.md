# 技术实现设计（Technical Design Doc, TDD）
> 适用范围：MVP（v1.0）及 v1.1/v1.2 演进。目标 iOS 16+（主打 iOS 26），以 **Liquid Glass** 视觉语言实现高帧率翻页时钟、番茄钟与生态集成（Widget/StandBy/Live Activities），并落地订阅 + 轻广告商业模式。

---

## 1. 架构总览
**架构风格**：清晰模块化 + Swift Concurrency（主）/Combine（少量兼容）+ SwiftUI 渲染。

**工程模块（Swift Package Manager 拆分）**
- `AppShell`：App 入口、DI 容器、场景管理、路由、Feature Flags
- `ClockEngine`：时间轴、翻页动画调度、OLED 防烧屏
- `ThemeKit`：主题/字体/色板/材质（Liquid Glass）系统
- `FocusKit`：番茄钟（状态机/计时器/统计）
- `AudioKit`：白噪音播放、无缝循环与功耗控制
- `EcoKit`：WidgetKit/ActivityKit/StandBy 适配
- `CommerceKit`：StoreKit 2 支付、权益缓存、价格实验开关
- `AdsKit`：广告适配层（可切 AdMob/自研/占位）
- `StorageKit`：配置/偏好/CoreData（或 SwiftData）
- `TelemetryKit`：匿名事件上报、崩溃/性能指标（可插拔）

**目标与扩展**
- Targets：`App`、`WidgetExtension`、`LiveActivitiesExtension`
- 最小化耦合：各子系统通过 `protocol` 定义接口（便于单元测试、替换实现）

**高层数据流**
```
        +--------------------+            +------------------+
        |   AppShell/DI     |--inject--> |   *Kit Modules   |
        +--------------------+            +------------------+
                  |                                 |
            ScenePhase                        State/Events
                  |                                 |
             +---------- SwiftUI View Tree (ClockView / Drawer / Settings / ThemeGrid) ----------+
                  |                                 |
           Render Loop                        User Intents
                  |                                 |
             ClockEngine  <--- ThemeKit --->  FocusKit / AudioKit
                  |                                 |
              EcoKit (Widget / Live Activities / StandBy)
```

---

## 2. 渲染与性能设计
### 2.1 翻页渲染管线
- **视图**：`FlipClockView`（SwiftUI）承载一个 `ClockLayerView`（`UIViewRepresentable`），后者为 `CALayer` 驱动的自定义层（或 MetalLayer 可选）。
- **时间步进**：`CADisplayLink`（同步屏幕刷新），以 `CACurrentMediaTime()` 校正至系统时间（避免 `Timer` 漂移）。
- **翻页实现**：
  - 将每个数字拆为 **上/下半页** 两层：`upperLayer`/`lowerLayer` 与 `hinge` 轴心。
  - 变换：上半页收起（fold out）/下半页展开（fold in），配合渐隐边缘与动态阴影。
  - 3D：`CATransform3D` + 透视（`m34`），阴影采样使用 `shadowPath` + 渐变 Mask 提升性能。
- **帧率**：目标 60fps，实测掉帧率 < 1%。在低电量或高温时自动降至 40–50fps（渐进降级）。
- **像素对齐**：数字栅格化（`shouldRasterize` 按需）、子像素对齐与整点切换“无跳动”。

### 2.2 Liquid Glass 材质
- **分层策略**：**时钟数字使用“纯净层”**（不折射/不模糊）；玻璃材质仅用于控件（按钮/抽屉/卡片/工具栏）。
- **材质实现**：
  - 高端机：自定义玻璃 Shader（Core Image + Metal Performance Shaders 可选），实现轻折射与高光带。
  - 兼容机：使用系统 `Material`（SwiftUI `.regularMaterial`/`.thinMaterial`）近似，叠加 `LinearGradient` 高光层。
- **三档视觉模式**：标准/舒适/纯色，通过主题参数切换透明度、对比度与高光强度，实时重绘。

### 2.3 OLED 防烧屏与省电
- **微位移**：每 60–90s 对时钟根层 `transform` 做 ≤ 2pt 的随机平移，保持可读布局。
- **降亮/降帧**：夜间/低电量/温度高时下调亮度与刷新频率；StandBy 时仅刷新必要层。
- **可见性控制**：依据 `ScenePhase`/`View.isVisible`，在不可见时暂停 `CADisplayLink`。

---

## 3. 功能模块设计
### 3.1 ClockEngine
- **职责**：系统时间采样、翻页调度、格式化（12/24h/日期/秒）、整点校正、微位移策略。
- **接口**：
```swift
protocol ClockEngine {
  var tick: AsyncStream<ClockSnapshot> { get }
  func start()
  func stop()
}
struct ClockSnapshot { let date: Date; let components: DateComponents; let shouldFlip: Bool }
```
- **算法要点**：
  - 每帧比较当前数值与上一帧，决定是否执行翻页动画（单位：位数粒度）。
  - 整点校正：在 `00:00:00` 前后 100ms 使用绝对时间对齐。

### 3.2 ThemeKit（含 Liquid Glass 参数）
- **结构**：`Theme`（颜色/字体/阴影/玻璃材质参数/高亮） + `ThemeManager`（动态切换）。
- **配置**：内置 JSON 描述主题，可远程下发（后续 v1.2）。
- **接口**：
```swift
struct GlassSpec { let blurRadius: CGFloat; let tint: Color; let highlight: Double; let refract: Double }
struct Theme { let name: String; let palette: Palette; let digitFont: Font; let glass: GlassSpec; let contrastMode: Contrast }
protocol ThemeProvider { var current: Theme { get }; func apply(_ theme: Theme) }
```

### 3.3 FocusKit（番茄钟）
- **状态机**：`idle → focus → break → focus …`，支持两个自定义预设（MVP）。
- **计时**：使用 `MonotonicClock`（`DispatchSourceTimer` 或 `Task.sleep`），与 UI 刷新解耦。
- **Live Activities**：`Activity<FocusAttributes>` 同步进度、暂停/继续操作。
- **统计**：本地聚合（天/周/月），v1.1 导出与图表。

### 3.4 AudioKit（白噪音）
- **播放器**：`AVAudioEngine` 或 `AVAudioPlayerNode`；文件预处理为无缝循环段（交叉淡化 10–50ms）。
- **后台**：启用 Audio 背景模式（默认关闭，用户开启后提示耗电策略）。
- **功耗**：采样率 44.1kHz，硬件解码优先，避免不必要的混音链路。

### 3.5 EcoKit（Widget/StandBy/Live Activities）
- **Widget**：`IntentConfiguration`（小/中/大）、锁屏小组件；时间线用 `TimelineProvider`，避免频繁刷新。
- **StandBy**：横置专用布局 + 低亮模式；仅重绘数字层与必要指示。
- **Activities**：锁屏/灵动岛显示剩余时间与操作按钮。

### 3.6 CommerceKit（StoreKit 2）
- **产品**：`pro.monthly` / `pro.yearly` / `lifetime`。
- **逻辑**：
  - 启动加载产品与本地权益缓存（Keychain）；
  - 购买 -> 验证（设备端 + 可选服务器端）-> 刷新权益；
  - 7 天试用（年付），退订原因采集（系统入口外的自填）。
- **接口**：
```swift
protocol Entitlements { var isPro: Bool { get } }
protocol CommerceService {
  func products() async throws -> [Product]
  func purchase(_ id: String) async throws -> PurchaseResult
  func restore() async throws
}
```

### 3.7 AdsKit（轻广告）
- **策略**：仅设置/主题页原生位；频控 ≤ 3 次/日；首装 7 天不展示。
- **适配**：`AdProvider` 协议封装，支持置空实现（方便地区/审核切换）。

### 3.8 StorageKit
- **偏好**：`UserDefaults`（主题、模式、上次位移偏移）。
- **数据**：CoreData（兼容 iOS16），模型：`FocusSession(id, start, end, type)`。
- **安全**：Keychain 存权益摘要/收据哈希；不存 PII。

### 3.9 TelemetryKit（匿名）
- **事件**：`AppOpen/ApplyTheme/StartPomodoro/CompletePomodoro/OpenPaywall/StartTrial/Purchase/CancelTrial/AdImpression/WidgetAdd`。
- **实现**：可选 TelemetryDeck/自建端点；GDPR/隐私合规，提供“关闭诊断”开关。

---

## 4. 关键算法与伪代码
### 4.1 时钟翻页调度
```swift
actor FlipScheduler {
  private var last: DateComponents?
  func snapshot(at now: Date) -> ClockSnapshot {
    let comps = calendar.dateComponents([.hour,.minute,.second], from: now)
    let shouldFlip = comps != last
    last = comps
    return ClockSnapshot(date: now, components: comps, shouldFlip: shouldFlip)
  }
}
```

### 4.2 OLED 防烧屏微位移
```swift
struct JitterSpec { let interval: ClosedRange<Double>; let delta: ClosedRange<CGFloat> }
final class BurnInProtector {
  private var nextAt: CFTimeInterval = 0
  func maybeJitter(layer: CALayer, now: CFTimeInterval, spec: JitterSpec) {
    if now > nextAt { layer.setAffineTransform(CGAffineTransform(translationX: .random(in: spec.delta),
                                                                  y: .random(in: spec.delta)))
      nextAt = now + .random(in: spec.interval)
    }
  }
}
```

### 4.3 白噪音无缝循环
- 预处理音频：离线拼接首尾 50ms 交叉淡化，写回 `*_looped.caf`。
- 播放端：`AVAudioPlayerNode.scheduleFile` 按节拍点连续串接，避免精度丢失。

---

## 5. API 契约（Swift）
```swift
// 应用层：视图模型与依赖注入
final class AppContainer {
  let clock: ClockEngine
  let focus: FocusService
  let audio: AudioService
  let theme: ThemeProvider
  let commerce: CommerceService
  let ads: AdProvider
}

// 视图模型（示例）
@MainActor final class ClockViewModel: ObservableObject {
  @Published var snapshot: ClockSnapshot = .init(date: .now, components: .init(), shouldFlip: false)
  func bind(engine: ClockEngine) {
    Task { for await s in engine.tick { self.snapshot = s } }
  }
}
```

---

## 6. 性能与质量基线
- **帧时间预算**：16.7ms（60fps）；UI 计算 ≤ 4ms、动画 ≤ 8ms、其他 ≤ 4ms。
- **内存**：< 150MB（时钟页 < 120MB）。
- **冷启动**：< 1.5s。
- **崩溃率**：< 0.3%。
- **功耗**：白噪音后台 2h 电量消耗 ≤ 8%（机型基线 iPhone 14）。
- **监控**：`os_signpost` + `MetricKit` + 崩溃平台（Sentry/Crashlytics 可选）。

---

## 7. 安全与合规
- **权限**：无位置/联系人/照片；仅通知（番茄提醒）与音频后台（用户开关）。
- **隐私**：不采集 PII；诊断可关闭；“应用隐私”填报为诊断数据（可选）。
- **商店合规**：不在核心时钟/专注页展示广告；不使用后台保活；购买/恢复流程完整。

---

## 8. 构建与持续集成
- **CI**：Xcode Cloud / GitHub Actions；触发单元测试、UI 测试、静态检查（SwiftLint/SwiftFormat）。
- **打包**：开发/预发/生产三环境；Feature Flags 通过远程 JSON（有签名）或本地开关。
- **签名**：独立 Widget/Activities 配置文件；App Groups 共享数据（后续版本用于同步）。

---

## 9. 测试策略
- **单测**：ClockEngine（整点校正/漂移）、FocusKit 状态机、CommerceKit 购买与恢复。
- **快照测试**：主要界面（不同主题/模式/语言/壁纸）；玻璃材质在“舒适/纯色”模式下基线比对。
- **性能**：翻页压力/温控；白噪音后台耗电曲线；Widget 时间线稳定性。
- **回归**：订阅 → 试用 → 续期/退订；广告频控；无障碍三开关即时生效。

---

## 10. 里程碑联动（与任务看板对应）
- **W1–2**：ClockEngine/Flip 原型 + ThemeKit Glass 初版 + 2 套主题
- **W3–4**：FocusKit/AudioKit 完成 + EcoKit（Widget/Activities/StandBy）
- **W5**：CommerceKit/AdsKit + 付费墙 + 事件埋点
- **W6**：性能/功耗打磨 + 无障碍/多语言 + 外测
- **W7–8**：上架物料/CI 稳定/灰度发布与首发联动

---

## 11. 附录
- **数据模型（CoreData）**
```
Entity FocusSession {
  id: UUID (PK)
  start: Date
  end: Date
  type: String  // focus/break
  device: String // 预留
}
```
- **主题 JSON（片段）**
```
{
  "name": "Neon",
  "digit": {"font": "SFProRounded-Heavy", "kerning": -0.5},
  "palette": {"bg":"#000000","fg":"#F5F7FA"},
  "glass": {"blur": 16, "tint":"#0FF2", "highlight": 0.35, "refract": 0.2},
  "contrast": "standard"
}
```
- **事件字典（Telemetry）**：见 PRD 埋点章节（保持一致）。

