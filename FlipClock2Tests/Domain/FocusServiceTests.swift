import XCTest
@testable import FlipClock2

final class FocusServiceTests: XCTestCase {
    @MainActor
    func testStartInitialisesState() async {
        let service = FocusService()
        let preset = FocusPreset(name: "Test", focusDuration: 120, restDuration: 60)
        service.start(with: preset)

        XCTAssertTrue(service.isRunning)
        XCTAssertEqual(service.snapshot.phase, .focus)
        XCTAssertEqual(service.snapshot.total, preset.focusDuration, accuracy: 0.1)
    }

    @MainActor
    func testPauseResume() async throws {
        let service = FocusService()
        let preset = FocusPreset(name: "Test", focusDuration: 5, restDuration: 5)
        service.start(with: preset)
        service.pause()
        XCTAssertTrue(service.snapshot.isPaused)
        service.resume()
        try await Task.sleep(nanoseconds: 300_000_000)
        XCTAssertFalse(service.snapshot.isPaused)
        service.stop()
    }
}
