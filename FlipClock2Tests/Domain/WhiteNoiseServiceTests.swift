import XCTest
@testable import FlipClock2

final class WhiteNoiseServiceTests: XCTestCase {
    @MainActor
    func testPlayStopsAndUpdatesState() {
        let service = WhiteNoiseService()
        service.play(profile: .rainfall)
        XCTAssertTrue(service.isPlaying)
        XCTAssertEqual(service.currentProfile, .rainfall)
        service.stop()
        XCTAssertFalse(service.isPlaying)
    }
}
