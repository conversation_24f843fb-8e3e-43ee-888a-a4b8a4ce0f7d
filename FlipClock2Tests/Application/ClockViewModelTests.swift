import XCTest
@testable import FlipClock2

final class ClockViewModelTests: XCTestCase {
    @MainActor
    func testStartInitialisesStream() async {
        let engine = MockClockEngine()
        let viewModel = ClockViewModel(engine: engine)
        viewModel.start()
        XCTAssertTrue(engine.startCalled)
        await Task.yield()
        engine.emit()
        XCTAssertEqual(viewModel.snapshot.shouldFlip, true)
    }
}

final class MockClockEngine: ClockEngine {
    var startCalled = false
    var stopCalled = false
    private var continuation: AsyncStream<ClockSnapshot>.Continuation?

    init() {
        let stream = AsyncStream<ClockSnapshot> { continuation in
            self.continuation = continuation
        }
        self.tickStream = stream
    }

    let tickStream: AsyncStream<ClockSnapshot>

    func start() {
        startCalled = true
    }

    func stop() {
        stopCalled = true
    }

    func emit() {
        continuation?.yield(.init(date: .now,
                                   components: DateComponents(hour: 1, minute: 2, second: 3),
                                   shouldFlip: true))
    }
}
